version: '3.8'

services:
  crownrms-web:
    build: .
    container_name: crownrms-web
    ports:
      - "8080:80"
    volumes:
      # 挂载网站文件以支持实时更新（开发模式）
      - ./cn:/usr/share/nginx/html/cn
      - ./favicon-16x16.png:/usr/share/nginx/html/favicon-16x16.png
      - ./favicon-32x32.png:/usr/share/nginx/html/favicon-32x32.png
      - ./apple-touch-icon.png:/usr/share/nginx/html/apple-touch-icon.png
      - ./mock.json:/usr/share/nginx/html/mock.json
      # 可选：挂载日志目录以便调试
      - ./logs:/var/log/nginx
    environment:
      - TZ=Asia/Shanghai
    restart: unless-stopped
    networks:
      - crownrms-network

networks:
  crownrms-network:
    driver: bridge 