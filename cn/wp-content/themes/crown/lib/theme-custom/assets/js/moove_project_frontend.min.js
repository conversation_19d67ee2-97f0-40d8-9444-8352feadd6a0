!(function (e) {
  e(document).ready(function () {
    if (
      (e(
        '.crw-form-section:not(.crw-form-section-ms) .crw-lg-form .select2.select2-bs',
      ).each(function () {
        e(this).select2({
          theme: 'bootstrap4',
          placeholder: {
            id: '-1',
            text: e(this).attr('placeholder'),
          },
          width: 'style',
          allowClear: <PERSON><PERSON>an(e(this).data('allow-clear')),
        })
      }),
      e(
        '.crw-form-section.crw-form-section-ms .crw-lg-form .select2.select2-bs',
      ).each(function () {
        e(this).select2({
          theme: 'bootstrap4-ms',
          placeholder: {
            id: '-1',
            text: e(this).attr('placeholder'),
          },
          width: 'style',
          allowClear: <PERSON><PERSON><PERSON>(e(this).data('allow-clear')),
        })
      }),
      e('.crw-lg-form form').on('submit', function (e) {
        jQuery('.crw-lg-form form')
          .find('button[type="submit"]')
          .addClass('disabled')
          .attr('disabled', !0)
      }),
      e('.crw-lg-form .select2.select2-def').each(function () {
        e(this).select2({
          width: 'style',
          placeholder: e(this).attr('placeholder'),
          allowClear: Boolean(e(this).data('allow-clear')),
        })
      }),
      e(document).on('input', '#crw_email', function (t) {
        e('#mic-verify').slideDown(300)
      }),
      e('.crw-multistep-form-wrap').length > 0)
    ) {
      document.getElementById('crw-multi-step-form')
      const t = document.querySelectorAll('.mfstep'),
        i = document.querySelectorAll('.next-step'),
        r = document.querySelectorAll('.prev-step')
      let n = 0
      i.forEach((i) => {
        i.addEventListener('click', () => {
          ;(function () {
            let i = !0
            t[n].querySelectorAll('input, textarea').forEach((t) => {
              t.checkValidity() ||
                ((i = !1), e(t).closest('.form-group').addClass('error'))
            }),
              e('#crw_services_ms').val().length ||
                ((i = !1),
                e('#crw_services_ms').closest('.form-group').addClass('error'))
            return i
          })() &&
            (t[n].classList.remove('active'),
            n++,
            n < t.length && t[n].classList.add('active'))
        })
      }),
        r.forEach((e) => {
          e.addEventListener('click', () => {
            t[n].classList.remove('active'), n--, t[n].classList.add('active')
          })
        }),
        t.forEach((e, t) => {
          t !== n ? e.classList.remove('active') : e.classList.add('active')
        }),
        e('#crw-multi-step-form').on(
          'change',
          'input, textarea, select',
          function () {
            e(this).closest('.form-group').removeClass('error')
          },
        )
    }
    function t(e, t) {
      return (
        (e = Math.ceil(e)),
        (t = Math.floor(t)),
        Math.floor(Math.random() * (t - e) + e)
      )
    }
    ;(jQuery('#mic-verify').each(function () {
      var e = jQuery(this)
      e.empty()
      var i = '',
        r = t(1, 9999),
        n = t(1, 24),
        s = e.attr('data-hu')
      ;(i += '<div class="mic-verify-w mic-left">'),
        (i += '<input type="hidden" name="enc_num" value="' + r + '">'),
        (i += '<input type="hidden" name="key_num" value="' + n + '">'),
        (i +=
          '<a class="refresh" id="verify-refresh-captcha" href=""><img src="' +
          e.attr('data-td') +
          '/dist/images/mic-refresh.png" alt="Refresh"></a>'),
        (i +=
          '<img id="verify_image" src="' +
          s +
          'wp-json/mic/v1/cpid?enn=' +
          r +
          '&ken=' +
          n +
          '" alt="Verification code" width="100" />'),
        (i += '</div>'),
        (i += '<div class="mic-verify-w mic-right">'),
        (i +=
          '<input type="text" name="mic_verify" id="mic_verify" required class="text"  placeholder="Enter the captcha" title="This confirms you are a human user and not a bot."/>'),
        (i += '</div>'),
        e.html(i)
    }),
    jQuery(document).on('click', '#verify-refresh-captcha', function (e) {
      e.preventDefault()
      var i = jQuery(this).closest('#mic-verify')
      i.empty()
      var r = '',
        n = t(1, 9999),
        s = t(1, 24),
        a = i.attr('data-hu')
      ;(r += '<div class="mic-verify-w mic-left">'),
        (r += '<input type="hidden" name="enc_num" value="' + n + '">'),
        (r += '<input type="hidden" name="key_num" value="' + s + '">'),
        (r +=
          '<a class="refresh" id="verify-refresh-captcha" href=""><img src="' +
          i.attr('data-td') +
          '/dist/images/mic-refresh.png" alt="Refresh"></a>'),
        (r +=
          '<img id="verify_image" src="' +
          a +
          'wp-json/mic/v1/cpid?enn=' +
          n +
          '&ken=' +
          s +
          '" alt="Verification code" width="100" />'),
        (r += '</div>'),
        (r += '<div class="mic-verify-w mic-right">'),
        (r +=
          '<input type="text" name="mic_verify" id="mic_verify" required class="text"  placeholder="Enter the captcha" title="This confirms you are a human user and not a bot."/>'),
        (r += '</div>'),
        i.html(r)
    }),
    e(document)
      .find('.crw-hero-carousel')
      .each(function () {
        var t = e(this).find('.swiper')
        if (t.length > 0) {
          new Swiper(t[0], {
            direction: 'horizontal',
            slidesPerView: 1.3,
            spaceBetween: 0,
            loop: !0,
            speed: 600,
            watchSlidesProgress: !0,
            effect: 'creative',
            creativeEffect: {
              limitProgress: 2,
              prev: {
                opacity: 1,
                scale: 1.2,
                padding: '0 500px',
                translate: ['-101%', 0, 0],
                origin: 'right center',
              },
              next: {
                opacity: 0.9,
                scale: 1,
                translate: ['102%', 0, 0],
                origin: 'left center',
              },
            },
            navigation: {
              nextEl:
                '.crw-hero-carousel .swiper-buttons-n .swiper-button-next-n',
              prevEl:
                '.crw-hero-carousel .swiper-buttons-n .swiper-button-prev-n',
            },
            pagination: {
              el: '.crw-hero-carousel .swiper-dots-n',
              clickable: !0,
            },
            on: {
              init: function () {
                let t = this.realIndex,
                  i = this.slides[t].clientWidth
                e('.crw-hero-carousel .swiper-navi').width(i)
              },
            },
          })
        }
      }),
    e(document)
      .find('.crw-hero-carousel-mobile')
      .each(function () {
        var t = e(this).find('.swiper')
        if (t.length > 0) {
          new Swiper(t[0], {
            direction: 'horizontal',
            slidesPerView: 1,
            spaceBetween: 0,
            loop: !0,
            watchSlidesProgress: !0,
            navigation: {
              nextEl:
                '.crw-hero-carousel-mobile .swiper-buttons-nm .swiper-button-next-nm',
              prevEl:
                '.crw-hero-carousel-mobile .swiper-buttons-nm .swiper-button-prev-nm',
              clickable: !0,
            },
            on: {
              init: function () {
                let t = this.realIndex,
                  i = this.slides[t].clientWidth
                e('.crw-hero-carousel-mobile .swiper-navi').width(i / 2)
              },
            },
          })
        }
      }),
    e(document)
      .find('.crw-testimonial-slider')
      .each(function () {
        var t = e(this).find('.swiper')
        if (t.length > 0) {
          new Swiper(t[0], {
            direction: 'horizontal',
            slidesPerView: 1,
            spaceBetween: 0,
            loop: !1,
            navigation: {
              nextEl: '.crw-testimonial-slider .swiper-button-next',
              prevEl: '.crw-testimonial-slider .swiper-button-prev',
            },
            pagination: {
              el: '.swiper-pagination',
              clickable: !0,
            },
          }).on('realIndexChange', function () {
            let t = this.realIndex,
              i = this.slides[t].dataset.url
            e('.crw-slider-wrap').css('background-image', "url('" + i + "')"),
              e('.crw-slider-bg-mobile').css(
                'background-image',
                "url('" + i + "')",
              )
          })
        }
      }),
    e(document)
      .find('.crw-card-slider')
      .each(function () {
        var t = e(this).find('.swiper')
        if (t.length > 0) {
          new Swiper(t[0], {
            direction: 'horizontal',
            slidesPerView: 1.4,
            spaceBetween: 30,
            loop: !1,
            navigation: {
              nextEl: '.crw-card-slider .swiper-button-next',
              prevEl: '.crw-card-slider .swiper-button-prev',
            },
            breakpoints: {
              769: {
                slidesPerView: 2.2,
                spaceBetween: 30,
              },
              1200: {
                slidesPerView: 2.7,
                spaceBetween: 30,
              },
            },
          })
        }
      }),
    e(document)
      .find('.crw-service-cards')
      .each(function () {
        var t = e(this).find('.swiper')
        if (t.length > 0) {
          new Swiper(t[0], {
            direction: 'horizontal',
            slidesPerView: 1.4,
            spaceBetween: 30,
            loop: !1,
            breakpoints: {
              769: {
                slidesPerView: 1.2,
                spaceBetween: 15,
              },
              1200: {
                slidesPerView: 4,
                spaceBetween: 30,
              },
            },
          })
        }
      }),
    e(document)
      .find('.crw-feature-cards-slider')
      .each(function () {
        var t = e(this).find('.swiper')
        if (t.length > 0) {
          const e = t.find('.swiper-slide').length,
            i = e <= 2 ? 1.4 : e <= 3 ? 3 : 3.5
          new Swiper(t[0], {
            direction: 'horizontal',
            slidesPerView: 1.4,
            spaceBetween: 30,
            loop: !0,
            navigation: {
              nextEl: '.crw-feature-cards-slider .swiper-button-next',
              prevEl: '.crw-feature-cards-slider .swiper-button-prev',
            },
            breakpoints: {
              800: {
                slidesPerView: 2.2,
                spaceBetween: 30,
              },
              1200: {
                slidesPerView: i,
                spaceBetween: 30,
              },
            },
          })
        }
      }),
    e(document)
      .find('.crw-latest-posts')
      .each(function () {
        var t = e(this).find('.swiper')
        if (t.length > 0) {
          new Swiper(t[0], {
            direction: 'horizontal',
            slidesPerView: 1.2,
            spaceBetween: 30,
            loop: !1,
            breakpoints: {
              800: {
                slidesPerView: 2.8,
                spaceBetween: 30,
              },
              1200: {
                slidesPerView: 4,
                spaceBetween: 0,
              },
            },
          })
        }
      }),
    e('#primary-mobile-menu').length > 0) &&
      new TraversableMenu({
        triggers: {
          parent_text: '<',
          top_text: '<',
        },
      }).initializeFromHTML('#primary-mobile-menu')
    ;-1 !== window.location.href.indexOf('gclid') &&
      (function (e, t, i) {
        var r = ''
        if (i) {
          var n = new Date()
          n.setTime(n.getTime() + 24 * i * 60 * 60 * 1e3),
            (r = '; expires=' + n.toUTCString())
        }
        document.cookie = e + '=' + (t || '') + r + '; path=/'
      })(
        'had_gclid',
        new URLSearchParams(window.location.search).get('gclid'),
        30,
      )
    e('#crw_gsrc').length > 0 &&
      null !==
        (function (e) {
          for (
            var t = e + '=', i = document.cookie.split(';'), r = 0;
            r < i.length;
            r++
          ) {
            for (var n = i[r]; ' ' == n.charAt(0); )
              n = n.substring(1, n.length)
            if (0 == n.indexOf(t)) return n.substring(t.length, n.length)
          }
          return null
        })('had_gclid') &&
      e('#crw_gsrc').val('Google PPC')
  })
})(jQuery)
