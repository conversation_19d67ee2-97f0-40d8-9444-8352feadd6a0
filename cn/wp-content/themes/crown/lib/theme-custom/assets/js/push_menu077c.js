! function(e, t) {
	if ("object" == typeof exports && "object" == typeof module) module.exports = t();
	else if ("function" == typeof define && define.amd) define([], t);
	else {
		var n = t();
		for (var i in n)("object" == typeof exports ? exports : e)[i] = n[i]
	}
}(self, (() => (() => {
	"use strict";
	var e = {
			905: function(e, t) {
				var n = this && this.__assign || function() {
						return n = Object.assign || function(e) {
							for (var t, n = 1, i = arguments.length; n < i; n++)
								for (var r in t = arguments[n]) Object.prototype.hasOwnProperty.call(t, r) && (e[r] = t[r]);
							return e
						}, n.apply(this, arguments)
					},
					i = this && this.__read || function(e, t) {
						var n = "function" == typeof Symbol && e[Symbol.iterator];
						if (!n) return e;
						var i, r, a = n.call(e),
							o = [];
						try {
							for (;
								(void 0 === t || t-- > 0) && !(i = a.next()).done;) o.push(i.value)
						} catch (e) {
							r = {
								error: e
							}
						} finally {
							try {
								i && !i.done && (n = a.return) && n.call(a)
							} finally {
								if (r) throw r.error
							}
						}
						return o
					},
					r = this && this.__spreadArray || function(e, t, n) {
						if (n || 2 === arguments.length)
							for (var i, r = 0, a = t.length; r < a; r++) !i && r in t || (i || (i = Array.prototype.slice.call(t, 0, r)), i[r] = t[r]);
						return e.concat(i || Array.prototype.slice.call(t))
					};
				Object.defineProperty(t, "__esModule", {
					value: !0
				});
				var a = function(e) {
						if ("object" == typeof e && null !== e) {
							if ("function" == typeof Object.getPrototypeOf) {
								var t = Object.getPrototypeOf(e);
								return t === Object.prototype || null === t
							}
							return "[object Object]" === Object.prototype.toString.call(e)
						}
						return !1
					},
					o = function() {
						for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t];
						return e.reduce((function(e, t) {
							if (Array.isArray(t)) throw new TypeError("Arguments provided to ts-deepmerge must be objects, not arrays.");
							return Object.keys(t).forEach((function(n) {
								["__proto__", "constructor", "prototype"].includes(n) || (Array.isArray(e[n]) && Array.isArray(t[n]) ? e[n] = o.options.mergeArrays ? Array.from(new Set(e[n].concat(t[n]))) : t[n] : a(e[n]) && a(t[n]) ? e[n] = o(e[n], t[n]) : e[n] = t[n])
							})), e
						}), {})
					},
					l = {
						mergeArrays: !0
					};
				o.options = l, o.withOptions = function(e) {
					for (var t = [], a = 1; a < arguments.length; a++) t[a - 1] = arguments[a];
					o.options = n({
						mergeArrays: !0
					}, e);
					var s = o.apply(void 0, r([], i(t), !1));
					return o.options = l, s
				}, t.default = o
			}
		},
		t = {};

	function n(i) {
		var r = t[i];
		if (void 0 !== r) return r.exports;
		var a = t[i] = {
			exports: {}
		};
		return e[i].call(a.exports, a, a.exports, n), a.exports
	}
	n.n = e => {
		var t = e && e.__esModule ? () => e.default : () => e;
		return n.d(t, {
			a: t
		}), t
	}, n.d = (e, t) => {
		for (var i in t) n.o(t, i) && !n.o(e, i) && Object.defineProperty(e, i, {
			enumerable: !0,
			get: t[i]
		})
	}, n.o = (e, t) => Object.prototype.hasOwnProperty.call(e, t), n.r = e => {
		"undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
			value: "Module"
		}), Object.defineProperty(e, "__esModule", {
			value: !0
		})
	};
	var i = {};
	return (() => {
		n.r(i), n.d(i, {
			TraversableMenu: () => Q,
			default: () => Q
		});
		var e = "data-traversable-id",
			t = "data-trigger-target",
			r = "data-trigger-source",
			a = "data-panel-height",
			o = "data-parent-id",
			l = "data-panel-id",
			s = "data-panel-depth";
		const p = function() {
				function t(e) {
					this.state = e
				}
				return t.elementIDGet = function(t) {
					return t && void 0 !== t.getAttribute ? t.getAttribute(e) : null
				}, t.elementApply = function(n, i) {
					i || (i = t.newID()), n.setAttribute(e, i)
				}, t.newID = function() {
					return "i_" + t.generateUniqueSerial()
				}, t.generateUniqueSerial = function() {
					return "xxxx-xxxx-xxx".replace(/[x]/g, (function() {
						return Math.floor(12 * Math.random()).toString(12)
					}))
				}, t
			}(),
			u = function() {
				function e(e, t) {
					this._depth = 0, this.container = e, this.state = t, this.menu_items = [], this._id = p.newID()
				}
				return Object.defineProperty(e.prototype, "id", {
					get: function() {
						return this._id
					},
					enumerable: !1,
					configurable: !0
				}), e.prototype.addMenuItem = function(e) {
					this.menu_items.push(e)
				}, Object.defineProperty(e.prototype, "depth", {
					get: function() {
						return this._depth
					},
					set: function(e) {
						this._depth = e
					},
					enumerable: !1,
					configurable: !0
				}), Object.defineProperty(e.prototype, "parent", {
					get: function() {
						return this._parent
					},
					set: function(e) {
						this._parent = e
					},
					enumerable: !1,
					configurable: !0
				}), e
			}(),
			c = function() {
				function e(e) {
					this.state = e, this.id = p.newID()
				}
				return Object.defineProperty(e.prototype, "panel", {
					get: function() {
						return this._panel
					},
					set: function(e) {
						this._panel = e
					},
					enumerable: !1,
					configurable: !0
				}), e
			}(),
			_ = function(e) {
				this.state = e, this.id = p.newID()
			},
			f = function() {
				function e() {}
				return e.replace = function(e, t, n) {
					return e.replace("[:" + t.toString() + ":]", n)
				}, e
			}();
		var d, m = (d = function(e, t) {
				return d = Object.setPrototypeOf || {
					__proto__: []
				}
				instanceof Array && function(e, t) {
					e.__proto__ = t
				} || function(e, t) {
					for (var n in t) Object.prototype.hasOwnProperty.call(t, n) && (e[n] = t[n])
				}, d(e, t)
			}, function(e, t) {
				if ("function" != typeof t && null !== t) throw new TypeError("Class extends value " + String(t) + " is not a constructor or null");

				function n() {
					this.constructor = e
				}
				d(e, t), e.prototype = null === t ? Object.create(t) : (n.prototype = t.prototype, new n)
			}),
			h = function() {
				function e(e) {
					this.state = e, this._id = p.newID()
				}
				return Object.defineProperty(e.prototype, "activates_panel", {
					get: function() {
						return this._activates_panel
					},
					set: function(e) {
						this._activates_panel = e
					},
					enumerable: !1,
					configurable: !0
				}), Object.defineProperty(e.prototype, "id", {
					get: function() {
						return this._id
					},
					enumerable: !1,
					configurable: !0
				}), Object.defineProperty(e.prototype, "text", {
					get: function() {
						return ""
					},
					enumerable: !1,
					configurable: !0
				}), Object.defineProperty(e.prototype, "classname", {
					get: function() {
						return ""
					},
					enumerable: !1,
					configurable: !0
				}), e
			}(),
			v = function(e) {
				function t(t) {
					return e.call(this, t) || this
				}
				return m(t, e), Object.defineProperty(t.prototype, "text", {
					get: function() {
						return this.state.options.triggers.child_text
					},
					enumerable: !1,
					configurable: !0
				}), Object.defineProperty(t.prototype, "classname", {
					get: function() {
						return this.state.options.classes.panel_trigger_child
					},
					enumerable: !1,
					configurable: !0
				}), t
			}(h),
			g = function(e) {
				function t(t) {
					return e.call(this, t) || this
				}
				return m(t, e), Object.defineProperty(t.prototype, "text", {
					get: function() {
						var e = "";
						if (this.panel && this.panel.depth < this.state.options.triggers.top_depth && this.state.options.triggers.top_remove_auto) e = this.state.options.triggers.top_text;
						else if (this.menu_item && this.menu_item.panel) {
							var t = this.menu_item.panel.menu_item_parent;
							if (t) {
								var n = t.link;
								e = f.replace(this.state.options.triggers.parent_text, "previous-title", n.text)
							}
						}
						return e
					},
					enumerable: !1,
					configurable: !0
				}), Object.defineProperty(t.prototype, "classname", {
					get: function() {
						return this.state.options.classes.panel_trigger_parent
					},
					enumerable: !1,
					configurable: !0
				}), t
			}(h),
			y = function(e) {
				function t(t) {
					return e.call(this, t) || this
				}
				return m(t, e), Object.defineProperty(t.prototype, "text", {
					get: function() {
						return this.state.options.triggers.top_text
					},
					enumerable: !1,
					configurable: !0
				}), Object.defineProperty(t.prototype, "classname", {
					get: function() {
						return this.state.options.classes.panel_trigger_top
					},
					enumerable: !1,
					configurable: !0
				}), t
			}(h);
		const b = function() {
			function e(e) {
				this.state = e, this.panels = [], this._id = p.newID()
			}
			return Object.defineProperty(e.prototype, "id", {
				get: function() {
					return this._id
				},
				enumerable: !1,
				configurable: !0
			}), e.prototype.validateItemData = function(e) {
				return !(!e || void 0 === e.items)
			}, e.prototype.initializeFromData = function(e) {
				if (!this.validateItemData(e)) throw "Invalid item data. Could not find items key";
				this.item_data = e, this.panels = [];
				var t = new u(this, this.state);
				this.item_data && void 0 !== this.item_data.items && this.panelInitializeFromData(t, this.item_data.items)
			}, e.prototype.panelInitializeFromData = function(e, t, n) {
				var i = this;
				void 0 === n && (n = 0), e.depth = n, this.addPanel(e), t.forEach((function(t) {
					var r = i.menuItemInitializeFromData(e, t);
					if (n > 0) {
						var a = new g(i.state);
						a.panel = e, a.activates_panel = e.parent, a.menu_item = e.menu_item_parent, e.trigger_parent = a
					}
					if (void 0 !== t.items) {
						var o = new u(i, i.state),
							l = new v(i.state);
						l.activates_panel = o, l.panel = e, o.parent = e, o.menu_item_parent = r, r.sub_panel = o, r.trigger_child = l, n++, i.panelInitializeFromData(o, t.items, n), n--
					}
				}))
			}, e.prototype.menuItemInitializeFromData = function(e, t) {
				var n = new c(this.state);
				if (n.panel = e, e.addMenuItem(n), void 0 !== t.link) {
					var i = new _(this.state);
					n.link = i, i.menu_item = n, i.text = t.text, i.link = t.link
				}
				return n
			}, e.prototype.addPanel = function(e) {
				this.panels.push(e)
			}, e.prototype.panelsReset = function() {
				this.panels = []
			}, e
		}();
		var A, x = {
				core: {
					debug: !1
				},
				init: {
					auto_traverse_to_active: !0
				},
				active: {
					find_by_url: !0,
					find_by_class: !0,
					urls: null,
					selectors: null,
					selectors_additional: [],
					parents_search_max: 10
				},
				selectors: {
					panel: ".menu__panel",
					panel_trigger_child: ".menu__panel__trigger--child",
					panel_trigger_parent: ".menu__panel__trigger--parent",
					panel_trigger_top: ".menu__panel__trigger--top",
					panel_title: ".menu__panel__title",
					panels_container: ".traversable-menu",
					menu_item_active: ".menu__item--active",
					menu_item_active_trail: ".menu__item--active-trail",
					menu_item: ".menu__item",
					menu_item_link: ".menu__item__link",
					menu_item_link_active: ".menu__item__link--active",
					tabbable_elements: "a"
				},
				triggers: {
					parent_text: "Up to [:previous-title:]",
					child_text: "Explore &gt;",
					top_text: "Up to Main Menu",
					top_depth: 2,
					top_remove_auto: !0,
					top_text_use_top_panel_title_at_first_level: !1,
					top_trigger_enabled: !0,
					parent_text_use_top_at_first_level: !0,
					events: ["keyup", "mouseup"]
				},
				accessibility: {
					container_role: "menubar",
					panel_role: "menu",
					menu_item_role: "menuitem",
					menu_item_link_focus_first: !0
				},
				panel: {
					auto_scroll_to_top: !0,
					height_auto: !0,
					zindex_auto: !0,
					zindex_start: 10,
					container_height_auto: !0,
					slide_animation_duration: 350,
					title_first: "",
					title_text: "[:menu-title:]",
					title_link_enabled: !0,
					title_enabled: !0,
					title_element: "h2"
				},
				panels_container: {
					height_auto: !0
				},
				classes: {
					panel: null,
					panels_container: null,
					panel_trigger_child: null,
					panel_trigger_parent: null,
					panel_trigger_top: null,
					menu_item: null,
					panels_initialized: "traversable-menu--initialized",
					panel_active: "menu__panel--active",
					panel_active_trail: "menu__panel--active-trail",
					panel_active_parent: "menu__panel--active-parent",
					panel_child_open: "menu__panel--child-open",
					panel_show_immediate: "-show-immediate",
					panel_depth: "menu__panel--depth-[:n:]",
					panel_height_auto_applied: "-panel-height-auto",
					panels_container_height_auto_applied: "-panels-container-height-auto",
					panel_title_link: "menu__panel__title__link",
					menu_item_link: "menu__item__link",
					menu_item_link_active: "menu__item__link--active"
				},
				render: {
					panels_container: null,
					depth_max: null,
					depth_max_relative: null
				},
				errors: {
					silent_if_no_container: !0
				},
				callbacks: {
					trigger: {
						before: null,
						on: null,
						after: null
					},
					panel: {
						activate: {
							before: null,
							after: null
						},
						initialize: {
							before: null,
							after: null
						},
						assimilate: {
							before: null,
							after: null
						}
					},
					panels: {
						initialize: {
							before: null,
							after: null
						}
					}
				}
			},
			I = function(e) {
				var t = e.split(".");
				return t[t.length - 1], e.toString().replace(/^\./, "")
			};
		for (A in x.selectors) void 0 !== x.classes[A] && null == x.classes[A] && (x.classes[A] = I(x.selectors[A]));
		const w = function() {
				function e(e) {
					this.depth_max_canonical = null, this.instance = e, this.options = x
				}
				return e.Instance_count = 0, e
			}(),
			k = function() {
				function n() {}
				return n.render = function(e, t) {
					var i = document.createElement("a");
					return n.applyAttributes(e, i, t), i
				}, n.applyAttributes = function(n, i, r) {
					var a = "";
					return n.activates_panel && (a = n.activates_panel.id), i.setAttribute(e, n.id), i.setAttribute(t, a), i.setAttribute("href", "#"), i.setAttribute("aria-haspopup", "true"), i.setAttribute("aria-expanded", "false"), i.setAttribute("aria-controls", a), i.innerHTML = n.text, i.classList.add(n.classname), n instanceof y && n.panel && n.panel.depth < r.options.triggers.top_depth && r.options.triggers.top_remove_auto && (i.style.display = "none"), i
				}, n.setActivated = function(e, t) {
					e.setAttribute("aria-expanded", "true")
				}, n
			}(),
			E = function() {
				function e(e) {
					this.state = e
				}
				return e.activeItemSelectors = function(t) {
					var n = [];
					if (null == t.options.active.selectors) {
						if (t.options.active.find_by_class && (n.push(t.options.selectors.menu_item_active), n.push(t.options.selectors.menu_item_link_active)), t.options.active.find_by_url && (n = n.concat(n, e.activeURLSelectors(t))), t.options.active.selectors_additional)
							for (var i = t.options.active.selectors_additional, r = 0; r < i.length; r++) n.push(i[r])
					} else n = t.options.active.selectors;
					return n
				}, e.activeURLsGet = function(t) {
					var n = [];
					if (null !== t.options.active.urls) {
						var i = t.options.active.urls;
						n = "function" == typeof i && "function" == typeof i.call ? n.concat(i.call(this)) : n.concat(i)
					} else n = e.activeURLsDefault(t);
					return n
				}, e.activeURLsDefault = function(e) {
					var t = [],
						n = window.location.href.split("#"),
						i = window.location.href;
					return t = [i = (i = i.replace(/^[A-Za-z0-9]+:\/\//, "")).substr(i.indexOf("/")), window.location.pathname, window.location.href], n.length > 1 && (t.push(n[0]), (n = n[0].split("?")).length > 1 && t.push(n[0])), (n = window.location.href.split("?")).length > 1 && t.push(n[0]), t
				}, e.activeURLSelectors = function(t) {
					for (var n = e.activeURLsGet(t), i = [], r = 0; r < n.length; r++) i.push('[href="' + n[r] + '"]');
					return i
				}, e
			}(),
			S = function() {
				function t() {}
				return t.render = function(e, n) {
					var i = document.createElement("a");
					return i.setAttribute("href", e.link), i.innerHTML = e.text, e.element = i, t.applyAttributes(i, e, n), i
				}, t.applyAttributes = function(t, n, i) {
					t.classList.add(i.options.classes.menu_item_link), t.setAttribute(e, n.id)
				}, t
			}(),
			P = function() {
				function t() {}
				return t.applyAttributes = function(t, n, i) {
					t.classList.add(i.options.classes.menu_item), t.setAttribute(e, n.id), t.setAttribute(l, n.panel.id)
				}, t.render = function(e, n) {
					var i = document.createElement("li");
					return t.applyAttributes(i, e, n), e.link && i.appendChild(S.render(e.link, n)), e.trigger_child && i.appendChild(k.render(e.trigger_child, n)), e.sub_panel && i.appendChild(O.render(e.sub_panel, n)), i
				}, t.activeMenuItemIdentify = function(e) {

					var t, n, i, r = E.activeItemSelectors(e),
						a = null;
					for (i = 0; i < r.length; i++) {
						var o = L.getContainerElement(e);
						// console.warn((t = o.querySelectorAll(r[i])).length);
						// console.warn(o.querySelectorAll(r[i]));
						if (o && (t = o.querySelectorAll(r[i])).length > 0 && (n = t[t.length - 1])) {
							if ( ! n.classList.contains('btn') ) {
								if (n.matches(e.options.selectors.menu_item)) a = n;
								else
									for (var l = e.options.active.parents_search_max, s = n.parentElement; l > 0;) {
										if (s.matches(e.options.selectors.menu_item)) {
											a = s;
											break
										}
										s = s.parentElement, l--
									}
								if (a) break
							}
						}
					}
					return a
				}, t.panelElementByActiveMenuItem = function(e) {
					var n = t.activeMenuItemIdentify(e);
					if (n) {
						var i = n.getAttribute(l),
							r = j.findElementByID(i);
						if (r) return r
					}
					return null
				}, t
			}(),
			T = function() {
				function e() {}
				return e.panelInclude = function(t, n) {
					t.classList.add(n.options.classes.panel_active_trail);
					var i = t.getAttribute(r);
					if (i) {
						var a = j.findElementByID(i);
						a && a.setAttribute("aria-expanded", "true")
					}
					var l = t.getAttribute(o);
					if (l) {
						var s = j.findElementByID(l);
						s && (s.classList.add(n.options.classes.panel_child_open), s.scrollTop = 0, e.panelInclude(s, n))
					}
				}, e.panelExclude = function(e, t) {
					e.classList.remove(t.options.classes.panel_active_trail), e.classList.remove(t.options.classes.panel_child_open);
					var n = e.getAttribute(r);
					if (n) {
						var i = j.findElementByID(n);
						i && i.setAttribute("aria-expanded", "false")
					}
				}, e.panelGetActive = function(e) {
					var t = L.getContainerElement(e);
					return t ? t.querySelector(".".concat(e.options.classes.panel_active)) : null
				}, e.recalculate = function(t) {
					var n = e.panelGetActive(t);
					L.getPanels(t).forEach((function(n) {
						var i = n;
						e.panelExclude(i, t)
					})), n && e.panelInclude(n, t)
				}, e
			}(),
			D = function() {
				function e() {}
				return e.render = function(t, n) {
					if (e.shouldShow(t, n)) {
						var i = document.createElement(n.options.panel.title_element),
							r = e.renderLink(i, t, n);
						return r ? (e.applyText(r, t, n), i.appendChild(r)) : e.applyText(i, t, n), e.applyAttributes(i, n), i
					}
					return null
				}, e.shouldShow = function(e, t) {
					return 0 == e.depth && "" != t.options.panel.title_first || !!(e.menu_item_parent && e.menu_item_parent.link && e.menu_item_parent.link.text)
				}, e.applyText = function(e, t, n) {
					e && (0 == t.depth && "" == e.innerHTML ? e.innerHTML = n.options.panel.title_first : t.menu_item_parent && t.menu_item_parent.link && t.menu_item_parent.link.text && (e.innerHTML = f.replace(n.options.panel.title_text, "menu-title", t.menu_item_parent.link.text)))
				}, e.renderLink = function(t, n, i) {
					var r;
					return t && n.menu_item_parent && n.menu_item_parent.link && n.menu_item_parent.link.link && (r = document.createElement("a"), e.applyLinkAttributes(r, n, i)), r || null
				}, e.applyLinkAttributes = function(e, t, n) {
					t.menu_item_parent && t.menu_item_parent.link && t.menu_item_parent.link.link && e.setAttribute("href", t.menu_item_parent.link.link)
				}, e.applyAttributes = function(e, t) {
					e.classList.add(t.options.classes.panel_title_link)
				}, e
			}(),
			O = function() {
				function t() {}
				return t.applyAttributes = function(t, n, i) {
					var a = i.options.classes.panel_depth.replace("[:n:]", n.depth.toString());
					t.setAttribute("id", n.id), t.setAttribute(e, n.id), t.setAttribute(s, n.depth.toString()), t.classList.add(i.options.classes.panel), t.classList.add(a), n.parent && t.setAttribute(o, n.parent.id), n.menu_item_parent && n.menu_item_parent.trigger_child && t.setAttribute(r, n.menu_item_parent.trigger_child.id), i.options.panel.zindex_auto && (t.style.zIndex = (n.depth + 1 + i.options.panel.zindex_start).toString()), i.options.panel.height_auto && (t.style.height = "0px")
				}, t.render = function(e, n) {
					var i = document.createElement("div"),
						r = document.createElement("ul");
					if (t.applyAttributes(i, e, n), e.depth >= n.options.triggers.top_depth && n.options.triggers.top_trigger_enabled) {
						var a = t.renderTopTrigger(e, n);
						i.appendChild(a)
					}
					if (e.trigger_parent) {
						var o = k.render(e.trigger_parent, n);
						i.appendChild(o)
					}
					var l = D.render(e, n);
					return l && i.appendChild(l), e.menu_items.forEach((function(e) {
						r.appendChild(P.render(e, n))
					})), i.appendChild(r), i
				}, t.renderTopTrigger = function(e, t) {
					var n = new y(t);
					return n.element = document.createElement("a"), n.activates_panel = t.panels_container.panels[0], e.trigger_top = n, k.render(n, t)
				}, t.activatePanelByID = function(e, n) {
					t.activate(j.findElementByID(e), n)
				}, t.activeAttributesApply = function(e, t) {
					e.classList.add(t.options.classes.panel_active), e.style.visibility = "visible", e.style.display = "initial", e.setAttribute("aria-hidden", "false"), e.setAttribute("data-panel-active", "true"), e.scrollTop = 0, T.panelInclude(e, t)
				}, t.activeAttributesRemove = function(e, n) {
					e.classList.remove(n.options.classes.panel_active), t.applyActionTimer(e, (function(e, t) {
						e.style.visibility = "hidden"
					}), n), e.setAttribute("aria-hidden", "true"), e.setAttribute("data-panel-active", "false"), e.scrollTop = 0
				}, t.removeActionTimer = function(n, i) {
					var r = n.getAttribute(e);
					void 0 !== t.action_timers[r] && Array.isArray(t.action_timers[r]) && (t.action_timers[r].forEach((function(e) {
						clearTimeout(e)
					})), t.action_timers[r] = [])
				}, t.applyActionTimer = function(n, i, r) {
					var a = n.getAttribute(e);
					void 0 !== t.action_timers[a] && Array.isArray(t.action_timers[a]) || (t.action_timers[a] = []), t.action_timers[a].push(setTimeout((function() {
						i.call(this, n, r)
					}), r.options.panel.slide_animation_duration))
				}, t.applyCalculatedHeight = function(e, n, i) {
					var r;
					return null !== (r = void 0 !== (i = i || {}).ignore_stored && 0 != i.ignore_stored || !e.hasAttribute(a) ? t.calculateHeight(e, n) : e.getAttribute(a)) && (e.style.height = r.toString() + "px"), r
				}, t.calculateHeight = function(e, t) {
					return e.scrollHeight
				}, t.activateDefault = function(e) {
					var n = !1;
					if (e.options.init.auto_traverse_to_active) {
						var i = P.panelElementByActiveMenuItem(e);
						i && (t.activate(i, e, {
							show_immediate: !0
						}), n = !0)
					}
					if (!n) {
						var r = L.getContainerElement(e);
						if (r) {
							var a = r.querySelector(e.options.selectors.panel);
							a && t.activate(a, e, {
								show_immediate: !0
							})
						}
					}
				}, t.activate = function(e, n, i) {
					var r = !(void 0 === (i = i || {}).show_immediate || !i.show_immediate),
						a = r ? 0 : n.options.panel.slide_animation_duration,
						o = !(void 0 === i.focus_first_item || !i.focus_first_item),
						l = L.getContainerElement(n);
					if (t.removeActionTimer(e, n), !t.panelIsActive(e, n)) {
						var p = t.panelGetActive(n),
							u = !1;
						if (p && (u = p.getAttribute(s) > e.getAttribute(s)), r)
							for (var c = l.querySelectorAll(n.options.selectors.panel), _ = 0; _ < c.length; _++) c[_].classList.add(n.options.classes.panel_show_immediate);
						if (t.panelsResetActive(n), t.activeAttributesApply(e, n), T.recalculate(n), n.options.panel.height_auto && (t.activeHeightApply(n), r ? (t.resetInactiveChildHeights(n), t.resetInactiveParentHeights(n)) : u ? (t.resetInactiveParentHeights(n), t.applyActionTimer(e, (function(e, n) {
								n.options.panel.height_auto && t.resetInactiveChildHeights(n)
							}), n)) : t.applyActionTimer(e, (function(e, n) {
								n.options.panel.height_auto && (t.resetInactiveParentHeights(n), t.resetInactiveChildHeights(n))
							}), n)), n.options.panel.auto_scroll_to_top && (l && (l.scrollTop = 0), e.scrollTop = 0), o && window.setTimeout((function() {
								if (n.options.accessibility.menu_item_link_focus_first) {
									var t = e.querySelector(n.options.selectors.menu_item_link);
									t && t.focus()
								}
							}), a), r)
							for (var f = l.querySelectorAll(".".concat(n.options.classes.panel_show_immediate)), d = 0; d < f.length; d++) f[d].classList.remove(n.options.classes.panel_show_immediate)
					}
				}, t.panelIsActive = function(e, t) {
					return e.classList.contains(t.options.classes.panel_active)
				}, t.panelGetActive = function(e) {
					var t = e.panels_container;
					return j.findElementByID(t.id).querySelector("." + e.options.classes.panel_active)
				}, t.panelsResetActive = function(e) {
					var n = e.panels_container,
						i = j.findElementByID(n.id);
					i && i.querySelectorAll("." + e.options.classes.panel_active).forEach((function(n) {
						t.activeAttributesRemove(n, e)
					}))
				}, t.activeHeightApply = function(e) {
					var n = t.panelGetActive(e);
					n && (t.applyCalculatedHeight(n, e), n.classList.add(e.options.classes.panel_height_auto_applied))
				}, t.resetInactiveParentHeights = function(e) {
					var n, i, r = t.panelGetActive(e);
					if (r) {
						for (i = r.getAttribute(a), n = t.panelGetParent(r, e); n;) n.style.height = i.toString() + "px", n.classList.add(e.options.classes.panel_height_auto_applied), n = t.panelGetParent(n, e);
						e.options.panels_container.height_auto && L.resize(e)
					}
				}, t.resetInactiveChildHeights = function(e) {
					var n = t.panelGetActive(e);
					if (n) {
						var i = n.querySelectorAll(e.options.selectors.panel);
						i.length > 0 && i.forEach((function(e) {
							e.style.height = "0px"
						})), e.options.panels_container.height_auto && L.resize(e)
					}
				}, t.panelGetParent = function(e, t) {
					return function(e, t) {
						if (void 0 !== e.parentElement) {
							var n = e.parentElement;
							if (n) {
								if (e instanceof Element) return n.closest(t);
								for (; n;) {
									if (n.matches(t)) return n;
									n = n.parentElement
								}
							}
						}
						return null
					}(e, t.options.selectors.panel)
				}, t.action_timers = {}, t
			}(),
			L = function() {
				function t() {}
				return t.applyAttributes = function(t, n, i) {
					t.setAttribute(e, n.id), t.classList.add(i.options.classes.panels_container)
				}, t.render = function(e, n) {
					var i = document.createElement("div");
					if (t.applyAttributes(i, e, n), e.panels.length > 0) {
						var r = O.render(e.panels[0], n);
						i.appendChild(r)
					}
					return i.classList.add(n.options.classes.panels_initialized), i
				}, t.getContainerElement = function(e) {
					return e.panels_container && e.panels_container.id ? j.findElementByID(e.panels_container.id) : null
				}, t.getPanels = function(e) {
					var n = t.getContainerElement(e);
					return n ? n.querySelectorAll(e.options.selectors.panel) : null
				}, t.resize = function(e) {
					var t = j.findElementByID(e.panels_container.id),
						n = O.panelGetActive(e),
						i = -1;
					return t && n && (-1 !== (i = O.calculateHeight(n, e)) && (t.style.height = i.toString() + "px"), t.classList.add(e.options.classes.panels_container_height_auto_applied)), i
				}, t.panelsHeightStore = function(e) {
					var n = t.getContainerElement(e).querySelectorAll(e.options.selectors.panel),
						i = 0;
					if (null !== n)
						for (var r = 0; r < n.length; r++) i = O.calculateHeight(n[r], e), n[r].setAttribute(a, i.toString())
				}, t
			}();
		var j = function() {
			function t() {}
			return t.findElementByID = function(t) {
				return t ? document.querySelector("[".concat(e, '="') + t.toString() + '"]') : false;
			}, t.render = function(e) {
				return L.render(e.state.panels_container, e.state)
			}, t.immediateChildren = function(e, t) {
				var n, i = e.parentElement ? e.parentElement : document;
				e.getAttribute("id") || (n = "elem_id_" + (Math.floor(9e4 * Math.random()) + 1e4).toString(), e.setAttribute("id", n));
				var r = "#" + e.getAttribute("id") + ">";
				r += t || "*";
				var a = i.querySelectorAll(r);
				return n && e.removeAttribute("id"), a
			}, t
		}();
		const C = function() {
				function e() {}
				return e.apply = function(e) {
					window.addEventListener("resize", (function() {
						e.options.panel.height_auto && L.panelsHeightStore(e), e.options.panels_container.height_auto && L.resize(e), e.options.panel.height_auto && (O.activeHeightApply(e), O.resetInactiveParentHeights(e))
					}))
				}, e
			}(),
			H = function() {
				function e() {}
				return e.eventShouldFire = function(e, t) {
					if (t.options.triggers.events.indexOf(e.type) > -1)
						if ("keyup" == e.type) {
							var n = e;
							if (void 0 !== n.code && "Enter" == n.key) return !0
						} else if ("mouseup" == e.type) return !0;
					return !1
				}, e.apply = function(t, n) {
					var i = n.options.callbacks.trigger.on;
					null == i && (i = e.eventHandler), ["keyup", "mouseup", "click"].forEach((function(e) {
						t.addEventListener(e, (function(e) {
							i.call(this, e, n)
						}), {
							capture: !1
						})
					}))
				}, e.eventHandler = function(n, i) {
					var r, a = {
						event: n,
						state: i
					};
					n.preventDefault(), e.eventShouldFire(n, i) && (r = i.options.callbacks.trigger.before) && r.call(this, a);
					var o, l = "";
					if (o = this.getAttribute(t)) {
						var s = j.findElementByID(o);
						if (s) {
							if ("keyup" == n.type) {
								var p = n;
								return n.preventDefault(), void 0 !== p.code && "Enter" == p.code && (l = "keyup", O.activate(s, i, {
									focus_first_item: !0
								})), !1
							}
							"mouseup" == n.type ? (console.log("mousing up"), n.preventDefault(), "touchend" != s.getAttribute("data-last-activation-event") && (l = "mouseup", O.activate(s, i))) : "click" == n.type && n.preventDefault()
						}
						s.setAttribute("data-last-activation-event", l)
					}
					return e.eventShouldFire(n, i) && (r = i.options.callbacks.trigger.after) && r.call(this, a), !1
				}, e
			}(),
			M = function() {
				function e() {}
				return e.apply = function(e, t) {
					if (e.trigger_child) {
						var n = j.findElementByID(e.trigger_child.id);
						n && H.apply(n, t)
					}
					e.sub_panel && z.apply(e.sub_panel, t)
				}, e
			}(),
			z = function() {
				function e() {}
				return e.apply = function(e, t) {
					if (e.trigger_parent) {
						var n = j.findElementByID(e.trigger_parent.id);
						n && H.apply(n, t)
					}
					if (e.trigger_top) {
						var i = j.findElementByID(e.trigger_top.id);
						i && H.apply(i, t)
					}
					e.menu_items.forEach((function(e) {
						M.apply(e, t)
					}))
				}, e
			}(),
			q = function() {
				function e() {}
				return e.apply = function(e) {
					var t = e.panels_container;
					z.apply(t.panels[0], e)
				}, e
			}(),
			B = function() {
				function e() {}
				return e.apply = function(e) {
					C.apply(e), q.apply(e)
				}, e
			}(),
			G = function() {
				function e() {}
				return e.parse = function(e, t, n) {
					var i = new _(n);
					i.menu_item = t, i.text = e.innerHTML, i.link = e.getAttribute("href"), t.link = i, S.applyAttributes(e, i, n)
				}, e
			}(),
			F = function() {
				function e() {}
				return e.parse = function(e, t, n) {
					k.applyAttributes(t, e, n)
				}, e
			}();
		const R = function() {
				function e() {}
				return e.parse = function(t, n, i) {
					var r = new c(i);
					r.element = t, r.panel = n, n.addMenuItem(r), P.applyAttributes(r.element, r, i);
					var a = r.element.querySelector(i.options.selectors.menu_item_link);
					a && G.parse(a, r, i);
					var o = r.element.querySelector(i.options.selectors.panel);
					if (o) {
						var l = new u(i.panels_container, i);
						l.parent = n, l.menu_item_parent = r, l.element = o, l.depth = n.depth + 1, r.sub_panel = l, e.parseTriggerChild(r, i), e.parseTriggerParent(r, i), U.parse(l, i)
					}
				}, e.parseTriggerChild = function(e, t) {
					var n = e.element.querySelector(t.options.selectors.panel_trigger_child);
					if (e.sub_panel && n) {
						var i = new v(t);
						i.panel = e.panel, i.activates_panel = e.sub_panel, i.element = n, i.menu_item = e, e.trigger_child = i, F.parse(n, i, t)
					}
				}, e.parseTriggerParent = function(e, t) {
					if (e.sub_panel) {
						var n = e.sub_panel.element.querySelector(t.options.selectors.panel_trigger_parent);
						if (n) {
							var i = new g(t);
							i.element = n, i.panel = e.sub_panel, i.activates_panel = e.sub_panel.parent, i.menu_item = e, e.sub_panel.trigger_parent = i, F.parse(n, i, t)
						}
					}
				}, e
			}(),
			U = function() {
				function e() {}
				return e.parse = function(t, n) {
					O.applyAttributes(t.element, t, n), e.parsePanelTitle(t, n), e.initializeTopTrigger(t, n), e.parsePanelMenuItems(t, n)
				}, e.parsePanelMenuItems = function(t, n) {
					var i = e.findImmediateMenuItems(t, n);
					i && i.forEach((function(e) {
						R.parse(e, t, n)
					}))
				}, e.findImmediateMenuItems = function(e, t) {
					var n, i = e.element.querySelector("ul");
					return i && (n = j.immediateChildren(i, t.options.selectors.menu_item)), n
				}, e.initializeTopTrigger = function(e, t) {
					var n = e.element.querySelector(t.options.selectors.panel_trigger_top);
					if (n) {
						var i = new y(t);
						i.panel = e, i.element = n, i.activates_panel = t.panels_container.panels[0], e.trigger_top = i, k.applyAttributes(i, n, t)
					}
				}, e.parsePanelTitle = function(e, t) {
					var n = e.element.querySelector(t.options.selectors.panel_title);
					n && (D.applyText(n, e, t), D.applyLinkAttributes(n, e, t), D.applyAttributes(n, t))
				}, e
			}(),
			Z = function() {
				function e() {}
				return e.parse = function(e, t) {
					var n = new b(t);
					n.element = e, t.panels_container = n, L.applyAttributes(n.element, t.panels_container, t);
					var i = e.querySelector(t.options.selectors.panel);
					if (i) {
						var r = new u(t.panels_container, t);
						r.element = i, r.depth = 0, t.panels_container.addPanel(r), U.parse(r, t)
					}
				}, e
			}(),
			J = function() {
				function e() {}
				return e.parse = function(e, t) {
					var n = document.querySelector(e);
					if (!n) return t.options.errors.silent_if_no_container || console.error("Could not find menu container. Selector was".concat(e)), !1;
					Z.parse(n, t)
				}, e
			}();
		var K = n(905),
			N = n.n(K),
			Q = function() {
				function e(e) {
					this.state = new w(this), e && this.optionsApply(e), w.Instance_count++, this.state.panels_container = new b(this.state)
				}
				return e.prototype.optionsApply = function(e) {
					this.state.options = N()(this.state.options, e)
				}, e.prototype.initializeFromHTML = function(e) {
					J.parse(e, this.state), this.state.options.panel.height_auto && L.panelsHeightStore(this.state), O.activateDefault(this.state), B.apply(this.state)
				}, e.prototype.initializeFromData = function(e) {
					this.state.panels_container.initializeFromData(e)
				}, e.prototype.render = function() {
					return j.render(this)
				}, e.prototype.renderInto = function(e) {
					if (!e || void 0 === e.appendChild) throw new Error("Could not append to given container. Check that you passed a valid, existing DOM element to renderInto");
					var t = this.render();
					e.appendChild(t), this.state.options.panel.height_auto && L.panelsHeightStore(this.state), O.activateDefault(this.state), B.apply(this.state)
				}, e.prototype.debug = function() {
					for (var e = [], t = 0; t < arguments.length; t++) e[t] = arguments[t];
					if (this.state.options.core.debug && e.length > 0)
						for (var n = 0; n < e.length; n++) console.log(e[n])
				}, e
			}()
	})(), i
})()));