(function($){$(document).ready(function(){$('.crw-lg-form .select2.select2-bs').each(function(){$(this).select2({theme:'bootstrap4',placeholder:{id:'-1',text:$(this).attr('placeholder')},width:'style',allowClear:<PERSON><PERSON><PERSON>($(this).data('allow-clear')),})});$(".crw-lg-form form").on("submit",function(event){jQuery(".crw-lg-form form").find('button[type="submit"]').addClass('disabled').attr('disabled',!0)});$('.crw-lg-form .select2.select2-def').each(function(){$(this).select2({width:'style',placeholder:$(this).attr('placeholder'),allowClear:Boolean($(this).data('allow-clear')),})});$(document).on('input','#crw_email',function(e){$('#mic-verify').slideDown(300)});function crw_int_get_random_int(min,max){min=Math.ceil(min);max=Math.floor(max);return Math.floor(Math.random()*(max-min)+min)}
jQuery('#mic-verify').each(function(){var verify_wrap=jQuery(this);verify_wrap.empty();var verify_html='';var enc_num=crw_int_get_random_int(1,9999);var key_num=crw_int_get_random_int(1,24);var home_url=verify_wrap.attr('data-hu');var theme_url=verify_wrap.attr('data-td');verify_html+='<div class="mic-verify-w mic-left">';verify_html+='<input type="hidden" name="enc_num" value="'+enc_num+'">';verify_html+='<input type="hidden" name="key_num" value="'+key_num+'">';verify_html+='<a class="refresh" id="verify-refresh-captcha" href=""><img src="'+theme_url+'/dist/images/mic-refresh.png" alt="Refresh"></a>';verify_html+='<img id="verify_image" src="'+home_url+'wp-json/mic/v1/cpid?enn='+enc_num+'&ken='+key_num+'" alt="Verification code" width="100" />';verify_html+='</div>';verify_html+='<div class="mic-verify-w mic-right">'
verify_html+='<input type="text" name="mic_verify" id="mic_verify" required class="text"  placeholder="Enter the captcha" title="This confirms you are a human user and not a bot."/>';verify_html+='</div>';verify_wrap.html(verify_html)});jQuery(document).on('click','#verify-refresh-captcha',function(e){e.preventDefault();var verify_wrap=jQuery(this).closest('#mic-verify');verify_wrap.empty();var verify_html='';var enc_num=crw_int_get_random_int(1,9999);var key_num=crw_int_get_random_int(1,24);var home_url=verify_wrap.attr('data-hu');var theme_url=verify_wrap.attr('data-td');verify_html+='<div class="mic-verify-w mic-left">';verify_html+='<input type="hidden" name="enc_num" value="'+enc_num+'">';verify_html+='<input type="hidden" name="key_num" value="'+key_num+'">';verify_html+='<a class="refresh" id="verify-refresh-captcha" href=""><img src="'+theme_url+'/dist/images/mic-refresh.png" alt="Refresh"></a>';verify_html+='<img id="verify_image" src="'+home_url+'wp-json/mic/v1/cpid?enn='+enc_num+'&ken='+key_num+'" alt="Verification code" width="100" />';verify_html+='</div>';verify_html+='<div class="mic-verify-w mic-right">'
verify_html+='<input type="text" name="mic_verify" id="mic_verify" required class="text"  placeholder="Enter the captcha" title="This confirms you are a human user and not a bot."/>';verify_html+='</div>';verify_wrap.html(verify_html)});$(document).find('.crw-hero-carousel').each(function(){var swiper=$(this).find('.swiper');if(swiper.length>0){const hero_swiper=new Swiper(swiper[0],{direction:'horizontal',slidesPerView:1.3,spaceBetween:0,loop:!0,speed:600,watchSlidesProgress:!0,effect:"creative",creativeEffect:{limitProgress:2,prev:{opacity:1,scale:1.2,padding:"0 500px",translate:["-101%",0,0],origin:'right center',},next:{opacity:0.9,scale:1,translate:["102%",0,0],origin:'left center',},},navigation:{nextEl:'.crw-hero-carousel .swiper-buttons-n .swiper-button-next-n',prevEl:'.crw-hero-carousel .swiper-buttons-n .swiper-button-prev-n',},pagination:{el:'.crw-hero-carousel .swiper-dots-n',clickable:!0,},on:{init:function(){let index=this.realIndex;let current_data=this.slides[index].clientWidth;$('.crw-hero-carousel .swiper-navi').width(current_data)},},})}});$(document).find('.crw-hero-carousel-mobile').each(function(){var swiper=$(this).find('.swiper');if(swiper.length>0){const hero_swiper_mobile=new Swiper(swiper[0],{direction:'horizontal',slidesPerView:1,spaceBetween:0,loop:!0,watchSlidesProgress:!0,navigation:{nextEl:'.crw-hero-carousel-mobile .swiper-buttons-nm .swiper-button-next-nm',prevEl:'.crw-hero-carousel-mobile .swiper-buttons-nm .swiper-button-prev-nm',clickable:!0,},on:{init:function(){let index=this.realIndex;let current_data=this.slides[index].clientWidth;$('.crw-hero-carousel-mobile .swiper-navi').width(current_data/2)},},})}});$(document).find('.crw-testimonial-slider').each(function(){var swiper=$(this).find('.swiper');if(swiper.length>0){const quote_swiper=new Swiper(swiper[0],{direction:'horizontal',slidesPerView:1,spaceBetween:0,loop:!1,navigation:{nextEl:'.crw-testimonial-slider .swiper-button-next',prevEl:'.crw-testimonial-slider .swiper-button-prev',},pagination:{el:'.swiper-pagination',clickable:!0,},});quote_swiper.on('realIndexChange',function(){let index=this.realIndex;let current_data=this.slides[index].dataset.url;$('.crw-slider-wrap').css("background-image","url('"+current_data+"')");$('.crw-slider-bg-mobile').css("background-image","url('"+current_data+"')")})}});$(document).find('.crw-card-slider').each(function(){var swiper=$(this).find('.swiper');if(swiper.length>0){const css_swiper=new Swiper(swiper[0],{direction:'horizontal',slidesPerView:1.4,spaceBetween:30,loop:!1,navigation:{nextEl:'.crw-card-slider .swiper-button-next',prevEl:'.crw-card-slider .swiper-button-prev',},breakpoints:{769:{slidesPerView:2.2,spaceBetween:30,},1200:{slidesPerView:2.7,spaceBetween:30,},},})}});$(document).find('.crw-feature-cards-slider').each(function(){var swiper=$(this).find('.swiper');if(swiper.length>0){const totalSlides=swiper.find('.swiper-slide').length;const slidesPerView=totalSlides<=2?1.4:totalSlides<=3?3:3.5;const card_slider=new Swiper(swiper[0],{direction:'horizontal',slidesPerView:1.4,spaceBetween:30,loop:!0,navigation:{nextEl:'.crw-feature-cards-slider .swiper-button-next',prevEl:'.crw-feature-cards-slider .swiper-button-prev',},breakpoints:{800:{slidesPerView:2.2,spaceBetween:30,},1200:{slidesPerView:slidesPerView,spaceBetween:30,},},})}});$(document).find('.crw-latest-posts').each(function(){var swiper=$(this).find('.swiper');if(swiper.length>0){const css_swiper=new Swiper(swiper[0],{direction:'horizontal',slidesPerView:1.2,spaceBetween:30,loop:!1,breakpoints:{800:{slidesPerView:2.8,spaceBetween:30,},1200:{slidesPerView:4,spaceBetween:0,},},})}});if($('#primary-mobile-menu').length>0){var traversable=new TraversableMenu({triggers:{'parent_text':'<','top_text':'<',},},);traversable.initializeFromHTML("#primary-mobile-menu")}
function cim_set_cookie(name,value,days){var expires="";if(days){var date=new Date();date.setTime(date.getTime()+(days*24*60*60*1000));expires="; expires="+date.toUTCString()}
document.cookie=name+"="+(value||"")+expires+"; path=/"}
function cim_get_cookie(name){var nameEQ=name+"=";var ca=document.cookie.split(';');for(var i=0;i<ca.length;i++){var c=ca[i];while(c.charAt(0)==' ')c=c.substring(1,c.length);if(c.indexOf(nameEQ)==0)return c.substring(nameEQ.length,c.length)}
return null}
if(window.location.href.indexOf('gclid')!==-1){var gclid=new URLSearchParams(window.location.search).get('gclid');cim_set_cookie('had_gclid',gclid,30)}
if($('#crw_gsrc').length>0){var gclid=cim_get_cookie('had_gclid');if(gclid!==null){$('#crw_gsrc').val('Google PPC')}}})})(jQuery)