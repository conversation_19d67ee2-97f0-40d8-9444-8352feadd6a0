(()=>{var h=class{static getScreenWidth(){return window.innerWidth||document.documentElement.clientWidth}static getScreenHeight(){return window.innerHeight||document.documentElement.clientHeight}static isNotValidScreensize(e,i){const t=this.getScreenWidth(),s=this.getScreenHeight(),n=e&&(t>i.width||s>i.height),r=!e&&(t<i.width||s<i.height);return n||r}static isPageCached(){const e=document.documentElement.nextSibling&&document.documentElement.nextSibling.data?document.documentElement.nextSibling.data:"";return e&&e.includes("Debug: cached")}static isIntersecting(e){return e.bottom>=0&&e.right>=0&&e.top<=(window.innerHeight||document.documentElement.clientHeight)&&e.left<=(window.innerWidth||document.documentElement.clientWidth)}static isPageScrolled(){return window.pageYOffset>0||document.documentElement.scrollTop>0}},l=h,d=class{constructor(e,i){this.config=e,this.performanceImages=[],this.logger=i}async run(){try{const e=this._generateLcpCandidates(1/0);e&&(this._initWithFirstElementWithInfo(e),this._fillATFWithoutDuplications(e))}catch(e){this.errorCode="script_error",this.logger.logMessage("Script Error: "+e)}}_generateLcpCandidates(e){const i=document.querySelectorAll(this.config.elements);return i.length<=0?[]:Array.from(i).map(n=>{if(n.nodeName.toLowerCase()==="img"&&n.parentElement.nodeName.toLowerCase()==="picture")return null;let r;if(n.nodeName.toLowerCase()==="picture"){const o=n.querySelector("img");if(o)r=o.getBoundingClientRect();else return null}else r=n.getBoundingClientRect();return{element:n,rect:r}}).filter(n=>n!==null).filter(n=>n.rect.width>0&&n.rect.height>0&&l.isIntersecting(n.rect)).map(n=>({item:n,area:this._getElementArea(n.rect),elementInfo:this._getElementInfo(n.element)})).sort((n,r)=>r.area-n.area).slice(0,e).map(n=>({element:n.item.element,elementInfo:n.elementInfo}))}_getElementArea(e){const i=Math.min(e.width,(window.innerWidth||document.documentElement.clientWidth)-e.left),t=Math.min(e.height,(window.innerHeight||document.documentElement.clientHeight)-e.top);return i*t}_getElementInfo(e){const i=e.nodeName.toLowerCase(),t={type:"",src:"",srcset:"",sizes:"",sources:[],bg_set:[],current_src:""},s=/url\(\s*?['"]?\s*?(.+?)\s*?["']?\s*?\)/ig;if(i==="img"&&e.srcset)t.type="img-srcset",t.src=e.src,t.srcset=e.srcset,t.sizes=e.sizes,t.current_src=e.currentSrc;else if(i==="img")t.type="img",t.src=e.src,t.current_src=e.currentSrc;else if(i==="video"){t.type="img";const n=e.querySelector("source");t.src=e.poster||(n?n.src:""),t.current_src=t.src}else if(i==="svg"){const n=e.querySelector("image");n&&(t.type="img",t.src=n.getAttribute("href")||"",t.current_src=t.src)}else if(i==="picture"){t.type="picture";const n=e.querySelector("img");t.src=n?n.src:"",t.sources=Array.from(e.querySelectorAll("source")).map(r=>({srcset:r.srcset||"",media:r.media||"",type:r.type||"",sizes:r.sizes||""}))}else{const r=[window.getComputedStyle(e,null).getPropertyValue("background-image"),getComputedStyle(e,":after").getPropertyValue("background-image"),getComputedStyle(e,":before").getPropertyValue("background-image")].filter(c=>c!=="none");if(r.length===0)return null;const o=r[0];if(t.type="bg-img",o.includes("image-set(")&&(t.type="bg-img-set"),!o||o===""||o.includes("data:image"))return null;const a=[...o.matchAll(s)];if(t.bg_set=a.map(c=>c[1]?{src:c[1].trim()+(c[2]?" "+c[2].trim():"")}:{}),t.bg_set.every(c=>c.src==="")&&(t.bg_set=a.map(c=>c[1]?{src:c[1].trim()}:{})),t.bg_set.length<=0)return null;t.bg_set.length>0&&(t.src=t.bg_set[0].src,t.type==="bg-img-set"&&(t.src=t.bg_set))}return t}_initWithFirstElementWithInfo(e){const i=e.find(t=>t.elementInfo!==null&&(t.elementInfo.src||t.elementInfo.srcset));if(!i){this.logger.logMessage("No LCP candidate found."),this.performanceImages=[];return}this.performanceImages=[{...i.elementInfo,label:"lcp"}]}_fillATFWithoutDuplications(e){e.forEach(({element:i,elementInfo:t})=>{this._isDuplicateImage(i)||!t||this.performanceImages.push({...t,label:"above-the-fold"})})}_isDuplicateImage(e){const i=this._getElementInfo(e);if(i===null)return!1;const t=i.type==="img"||i.type==="img-srcset"||i.type==="video",s=i.type==="bg-img"||i.type==="bg-img-set"||i.type==="picture";return(t||s)&&this.performanceImages.some(n=>n.src===i.src)}getResults(){return this.performanceImages}},f=d,m=class{constructor(e,i){this.config=e,this.logger=i,this.lazyRenderElements=[]}async run(){try{const e=this._getLazyRenderElements();e&&this._processElements(e)}catch(e){this.errorCode="script_error",this.logger.logMessage("Script Error: "+e)}}_getLazyRenderElements(){const e=document.querySelectorAll("[data-rocket-location-hash]"),i=this._getSvgUseTargets();return e.length<=0?[]:Array.from(e).filter(s=>this._skipElement(s)?!1:i.includes(s)?(this.logger.logColoredMessage(`Element skipped because of SVG: ${s.tagName}`,"orange"),!1):!0).map(s=>({element:s,depth:this._getElementDepth(s),distance:this._getElementDistance(s),hash:this._getLocationHash(s)}))}_getElementDepth(e){let i=0,t=e.parentElement;for(;t;)i++,t=t.parentElement;return i}_getElementDistance(e){const i=e.getBoundingClientRect(),t=window.pageYOffset||document.documentElement.scrollTop;return Math.max(0,i.top+t-l.getScreenHeight())}_skipElement(e){const i=this.config.skipStrings||["memex"];return!e||!e.id?!1:i.some(t=>e.id.toLowerCase().includes(t.toLowerCase()))}_shouldSkipElement(e,i){if(!e)return!1;for(let t=0;t<i.length;t++){const[s,n]=i[t],r=e.getAttribute(s);if(r&&new RegExp(n,"i").test(r))return!0}return!1}_checkLcrConflict(e){const i=[],t=window.getComputedStyle(e),n=["marginTop","marginRight","marginBottom","marginLeft"].some(o=>parseFloat(t[o])<0);return(n||t.contentVisibility==="auto"||t.contentVisibility==="hidden")&&i.push({element:e,conflicts:[n&&"negative margin",t.contentVisibility==="auto"&&"content-visibility:auto",t.contentVisibility==="hidden"&&"content-visibility:hidden"].filter(Boolean)}),Array.from(e.children).forEach(o=>{const a=window.getComputedStyle(o),u=["marginTop","marginRight","marginBottom","marginLeft"].some(E=>parseFloat(a[E])<0);(u||a.position==="absolute"||a.position==="fixed")&&i.push({element:o,conflicts:[u&&"negative margin",a.position==="absolute"&&"position:absolute",a.position==="fixed"&&"position:fixed"].filter(Boolean)})}),i}_processElements(e){e.forEach(({element:i,depth:t,distance:s,hash:n})=>{if(this._shouldSkipElement(i,this.config.exclusions||[])||n==="No hash detected")return;const r=this._checkLcrConflict(i);if(r.length>0){this.logger.logMessage("Skipping element due to conflicts:",r);return}const o=i.parentElement&&this._getElementDistance(i.parentElement)<this.config.lrc_threshold&&s>=this.config.lrc_threshold,a=o?"green":s===0?"red":"";this.logger.logColoredMessage(`${"	".repeat(t)}${i.tagName} (Depth: ${t}, Distance from viewport bottom: ${s}px)`,a),this.logger.logColoredMessage(`${"	".repeat(t)}Location hash: ${n}`,a),this.logger.logColoredMessage(`${"	".repeat(t)}Dimensions Client Height: ${i.clientHeight}`,a),o&&(this.lazyRenderElements.push(n),this.logger.logMessage(`Element pushed with hash: ${n}`))})}_getXPath(e){return e&&e.id!==""?`//*[@id="${e.id}"]`:this._getElementXPath(e)}_getElementXPath(e){if(e===document.body)return"/html/body";const i=this._getElementPosition(e);return`${this._getElementXPath(e.parentNode)}/${e.nodeName.toLowerCase()}[${i}]`}_getElementPosition(e){let i=1,t=e.previousElementSibling;for(;t;)t.nodeName===e.nodeName&&i++,t=t.previousElementSibling;return i}_getLocationHash(e){return e.hasAttribute("data-rocket-location-hash")?e.getAttribute("data-rocket-location-hash"):"No hash detected"}_getSvgUseTargets(){const e=document.querySelectorAll("use"),i=new Set;return e.forEach(t=>{let s=t.parentElement;for(;s&&s!==document.body;)i.add(s),s=s.parentElement}),Array.from(i)}getResults(){return this.lazyRenderElements}},p=m,_=class{constructor(e){this.enabled=e}logMessage(e){this.enabled&&console.log(e)}logColoredMessage(e,i="green"){this.enabled&&console.log(`%c${e}`,`color: ${i};`)}},b=_,y=class{constructor(e){this.config=e,this.lcpBeacon=null,this.lrcBeacon=null,this.infiniteLoopId=null,this.errorCode="",this.logger=new b(this.config.debug)}async init(){if(this.scriptTimer=new Date,!await this._isValidPreconditions()){this._finalize();return}if(l.isPageScrolled()){this.logger.logMessage("Bailing out because the page has been scrolled"),this._finalize();return}this.infiniteLoopId=setTimeout(()=>{this._handleInfiniteLoop()},1e4);const e=await this._getGeneratedBefore(),i=this.config.status.atf&&(e===!1||e.lcp===!1),t=this.config.status.lrc&&(e===!1||e.lrc===!1);i?(this.lcpBeacon=new f(this.config,this.logger),await this.lcpBeacon.run()):this.logger.logMessage("Not running BeaconLcp because data is already available or feature is disabled"),t?(this.lrcBeacon=new p(this.config,this.logger),await this.lrcBeacon.run()):this.logger.logMessage("Not running BeaconLrc because data is already available or feature is disabled"),i||t?this._saveFinalResultIntoDB():(this.logger.logMessage("Not saving results into DB as no beacon features ran."),this._finalize())}async _isValidPreconditions(){const e={width:this.config.width_threshold,height:this.config.height_threshold};return l.isNotValidScreensize(this.config.is_mobile,e)?(this.logger.logMessage("Bailing out because screen size is not acceptable"),!1):!0}async _getGeneratedBefore(){if(!l.isPageCached())return!1;let e=new FormData;return e.append("action","rocket_check_beacon"),e.append("rocket_beacon_nonce",this.config.nonce),e.append("url",this.config.url),e.append("is_mobile",this.config.is_mobile),(await fetch(this.config.ajax_url,{method:"POST",credentials:"same-origin",body:e}).then(t=>t.json())).data}_saveFinalResultIntoDB(){const e={lcp:this.lcpBeacon?this.lcpBeacon.getResults():null,lrc:this.lrcBeacon?this.lrcBeacon.getResults():null},i=new FormData;i.append("action","rocket_beacon"),i.append("rocket_beacon_nonce",this.config.nonce),i.append("url",this.config.url),i.append("is_mobile",this.config.is_mobile),i.append("status",this._getFinalStatus()),i.append("results",JSON.stringify(e)),fetch(this.config.ajax_url,{method:"POST",credentials:"same-origin",body:i,headers:{"wpr-saas-no-intercept":!0}}).then(t=>t.json()).then(t=>{this.logger.logMessage(t.data.lcp)}).catch(t=>{this.logger.logMessage(t)}).finally(()=>{this._finalize()})}_getFinalStatus(){return this.errorCode!==""?this.errorCode:10<=(new Date-this.scriptTimer)/1e3?"timeout":"success"}_handleInfiniteLoop(){this._saveFinalResultIntoDB()}_finalize(){document.querySelector('[data-name="wpr-wpr-beacon"]').setAttribute("beacon-completed","true"),clearTimeout(this.infiniteLoopId)}},g=y;(e=>{if(!e)return;const i=new g(e);if(document.readyState!=="loading"){setTimeout(()=>{i.init()},e.delay);return}document.addEventListener("DOMContentLoaded",()=>{setTimeout(()=>{i.init()},e.delay)})})(window.rocket_beacon_data);var w=g})();
//# sourceMappingURL=wpr-beacon.min.js.map
