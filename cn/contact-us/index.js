// 获取国家数据
function getCustomCountry() {
  return fetch(`/prod-api/api/web/system/dict/data/type/custom_country`)
    .then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`)
      }
      return res.json()
    })
    .then((data) => {
      const countries = data.data || []
      return countries
    })
    .catch((error) => {
      console.error('获取国家数据失败:', error)
      return []
    })
}

// 获取服务数据
function getCustomService() {
  return fetch(`/prod-api/api/web/system/dict/data/type/custom_service`)
    .then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`)
      }
      return res.json()
    })
    .then((data) => {
      const services = data.data || []
      return services
    })
    .catch((error) => {
      console.error('获取服务数据失败:', error)
      return []
    })
}

// 渲染国家下拉框
function renderCountryOptions(countries) {
  const countrySelect = document.getElementById('crwCountryCode')
  if (!countrySelect) {
    console.warn('未找到国家下拉框元素')
    return
  }

  countrySelect.innerHTML = '' // 清空现有选项

  // 添加国家选项
  countries.forEach((country) => {
    const option = document.createElement('option')
    option.value = country.dictValue
    option.textContent = country.dictLabel

    // 如果是中国，设为默认选中
    if (country.dictLabel === '中国') {
      option.selected = true
    }

    countrySelect.appendChild(option)
  })

  console.log('国家下拉框已更新，共', countries.length, '个国家')
}

// 渲染服务下拉框
function renderServiceOptions(services) {
  const serviceSelect = document.getElementById('crwServices')
  if (!serviceSelect) {
    console.warn('未找到服务下拉框元素')
    return
  }
  serviceSelect.innerHTML = '' // 清空现有选项
  // 添加服务选项
  services.forEach((service) => {
    const option = document.createElement('option')
    option.value = service.dictValue
    option.textContent = service.dictLabel
    serviceSelect.appendChild(option)
  })

  console.log('服务下拉框已更新，共', services.length, '个服务')
}

// 初始化国家下拉框
function initializeSelect() {
  getCustomCountry()
    .then((countries) => {
      if (countries) {
        renderCountryOptions(countries)
      } else {
        console.warn('未获取到国家数据')
      }
    })
    .catch((error) => {
      console.error('初始化国家下拉框失败:', error)
    })
  getCustomService()
    .then((services) => {
      if (services) {
        renderServiceOptions(services)
      } else {
        console.warn('未获取到服务数据')
      }
    })
    .catch((error) => {
      console.error('初始化服务下拉框失败:', error)
    })
}

// 表单处理相关函数
function initializeFormHandler() {
  const form = document.querySelector('form')
  const submitBtn = document.querySelector('button[type="submit"]')

  if (form && submitBtn) {
    submitBtn.addEventListener('click', function (e) {
      e.preventDefault()

      // 验证表单
      if (!validateForm()) {
        return
      }

      // 收集表单数据
      const formData = new FormData(form)
      const data = {}

      // 将FormData转换为普通对象，处理多选字段
      for (let [key, value] of formData.entries()) {
        // 检查是否是多选字段crwServices[]
        if (key === 'crwServices[]') {
          // 将字段名标准化为crwServices（去掉方括号）
          const normalizedKey = 'crwServices'
          // 如果已经存在该字段，将其转换为数组并添加新值
          if (data[normalizedKey]) {
            if (Array.isArray(data[normalizedKey])) {
              data[normalizedKey].push(value)
            } else {
              data[normalizedKey] = [data[normalizedKey], value]
            }
          } else {
            // 第一次遇到该字段，创建数组
            data[normalizedKey] = [value]
          }
        } else {
          data[key] = value
        }
      }

      data.crwServices = data.crwServices.join(',') // 将数组转换为逗号分隔的字符串

      // 调试：打印收集到的数据
      console.log('收集到的表单数据:', data)
      console.log('crwServices数据类型:', typeof data.crwServices)
      console.log('crwServices是否为数组:', Array.isArray(data.crwServices))
      console.log('crwServices内容:', data.crwServices)

      // 禁用提交按钮，防止重复提交
      submitBtn.disabled = true
      submitBtn.textContent = '提交中...'

      // 发送请求
      fetch('/prod-api/api/web/customer/info', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error('网络请求失败')
          }
          return response.json()
        })
        .then(() => {
          // 处理成功响应
          showSuccessMessage('提交成功！')

          // 重置表单
          resetForm(form)
          // 显示下载容器（如果存在）
          const successMessage = document.getElementById('rsp--message-sv')
          if (successMessage) {
            successMessage.style.display = 'block'
          }
          initializeSelect()
        })
        .catch((error) => {
          // 处理错误
          console.error('提交失败:', error)
          showErrorMessage('提交失败，请稍后重试')
        })
        .finally(() => {
          // 恢复提交按钮
          submitBtn.disabled = false
          submitBtn.textContent = '提交'
        })
    })
  }
}

// 重置表单函数
function resetForm(form) {
  // 重置普通表单元素
  form.reset()

  // 重置Select2下拉框
  const select2Elements = form.querySelectorAll('.select2')
  select2Elements.forEach((element) => {
    try {
      // 检查jQuery和Select2是否可用
      if (typeof $ !== 'undefined' && $(element).data('select2')) {
        // 清除选择
        $(element).val(null).trigger('change')

        // 特别处理多选下拉框
        if (element.hasAttribute('multiple')) {
          $(element).val([]).trigger('change')
        }
      } else {
        // 如果Select2不可用，直接重置原生select元素
        element.selectedIndex = -1
        if (element.hasAttribute('multiple')) {
          Array.from(element.options).forEach((option) => {
            option.selected = false
          })
        }
      }
    } catch (error) {
      console.warn('重置Select2下拉框时出错:', error)
      // 降级到原生重置
      element.selectedIndex = -1
    }
  })

  // 重置国家下拉框为默认值（中国）
  const countrySelect = document.getElementById('crwCountryCode')
  if (countrySelect) {
    try {
      // 查找中国选项并设为默认
      const chinaOption = Array.from(countrySelect.options).find((option) =>
        option.textContent.includes('中国'),
      )
      if (chinaOption) {
        if (typeof $ !== 'undefined' && $(countrySelect).data('select2')) {
          $(countrySelect).val(chinaOption.value).trigger('change')
        } else {
          countrySelect.value = chinaOption.value
        }
      }
    } catch (error) {
      console.warn('重置国家下拉框时出错:', error)
    }
  }

  // 清除所有错误样式
  const formFields = form.querySelectorAll('input, select, textarea')
  formFields.forEach((field) => {
    field.style.borderColor = ''
  })

  console.log('表单已重置，包括Select2下拉框')
}

// 表单验证函数
function validateForm() {
  const requiredFields = [
    'crwFirstName',
    'crwLastName',
    'crwJobTitle',
    'crwCompany',
    'crwCountryCode',
    'crwEmail',
    'crwServices',
  ]

  let isValid = true

  // 清除之前的错误样式
  document.querySelectorAll('.error').forEach((el) => {
    el.style.display = 'none'
  })

  // 验证必填字段
  requiredFields.forEach((fieldName) => {
    const field = document.getElementById(fieldName)
    if (field && !field.value.trim()) {
      isValid = false
      field.style.borderColor = 'red'
      // 可以在这里添加错误提示
    } else if (field) {
      field.style.borderColor = ''
    }
  })

  // 验证邮箱格式
  const emailField = document.getElementById('crwEmail')
  if (emailField && emailField.value.trim()) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(emailField.value.trim())) {
      isValid = false
      emailField.style.borderColor = 'red'
      showErrorMessage('请输入有效的邮箱地址')
    }
  }

  if (!isValid) {
    showErrorMessage('请填写所有必填字段')
  }

  return isValid
}

// 显示成功消息
function showSuccessMessage(message) {
  showMessage(message, '#28a745')
}

// 显示错误消息
function showErrorMessage(message) {
  showMessage(message, '#dc3545')
}

// 通用消息显示函数
function showMessage(message, backgroundColor) {
  // 创建临时提示元素
  const toast = document.createElement('div')
  toast.textContent = message
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${backgroundColor};
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  `

  document.body.appendChild(toast)

  // 3秒后移除提示
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast)
    }
  }, 3000)
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
  // 初始化表单处理器
  initializeFormHandler()

  // 初始化国家下拉框
  initializeSelect()

  console.log('联系我们页面已初始化')
})
