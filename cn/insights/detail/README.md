# 动态数据加载功能说明

## 概述

本功能实现了页面内容的动态加载，将原本静态的"2020 安全销毁指引"等文本内容替换为通过 API 接口动态获取的数据。

## 文件结构

```
cn/insights/detail/
├── index.html          # 主页面文件
├── index.js            # 动态数据加载脚本
├── mock-api.js         # 模拟API数据（开发环境使用）
├── test.html           # 测试页面
└── README.md           # 说明文档
```

## 功能特性

### 1. 动态内容替换

- **页面标题**: 动态更新浏览器标题栏和 meta 标签
- **面包屑导航**: 更新导航路径中的文章标题
- **主标题**: 更新页面主要标题（h1 标签）
- **发布状态**: 将"Published"替换为数据中的`publishTime`字段
- **发布日期**: 格式化显示文章发布日期
- **文章内容**: 将`#article-content`下的内容替换为数据中的`content`字段
- **特色图片**: 更新文章配图
- **分类标签**: 动态生成文章分类链接
- **下载表单标题**: 更新表单区域标题

### 2. 开发/生产模式切换

- **开发模式**: 使用本地模拟数据，便于开发和测试
- **生产模式**: 调用真实 API 接口获取数据
- 通过 `CONFIG.isDevelopment` 配置项控制

### 3. 错误处理

- 网络请求失败时的优雅降级
- 数据格式异常的处理
- 控制台日志记录便于调试

## 使用方法

### 1. 基本使用

在 HTML 页面中引入必要的脚本文件：

```html
<!-- 开发环境下引入模拟数据 -->
<script src="mock-api.js"></script>
<!-- 引入主要功能脚本 -->
<script src="index.js"></script>
```

### 2. 配置 API 接口

在 `index.js` 中修改配置：

```javascript
const CONFIG = {
  // 开发模式：true使用模拟数据，false使用真实API
  isDevelopment: false, // 生产环境设置为false
  apiBaseUrl: '/prod-api/api/dev-api/api/web/doc/news',
}
```

### 3. URL 参数

通过 URL 参数 `id` 指定要加载的新闻 ID：

```
http://example.com/cn/insights/detail/index.html?id=123
```

如果未提供 ID 参数，默认使用 ID 为"1"的新闻。

## API 数据格式

接口应返回以下格式的 JSON 数据：

```json
{
  "success": true,
  "data": {
    "title": "文章标题",
    "publishTime": "已发布",
    "publishDate": "2024-03-15T08:00:00Z",
    "content": "文章内容（支持HTML）",
    "featuredImage": "图片URL",
    "categories": ["分类1", "分类2"],
    "downloadTitle": "下载按钮标题"
  }
}
```

### 字段说明

- **title**: 文章标题，用于更新页面标题、面包屑导航和主标题
- **publishTime**: 发布状态文本，替换原来的"Published"
- **publishDate**: 发布日期（ISO 格式），用于格式化显示发布日期
- **content**: 文章内容，支持 HTML 格式，替换`#article-content`下的内容
- **featuredImage**: 特色图片 URL
- **categories**: 文章分类数组
- **downloadTitle**: 下载表单标题

## 测试

### 1. 本地测试

启动本地服务器：

```bash
cd cn/insights/detail
python3 -m http.server 8000
```

访问测试页面：

```
http://localhost:8000/test.html
```

### 2. 测试功能

测试页面提供了以下测试按钮：

- **加载新闻 1**: 测试数据安全指南内容
- **加载新闻 2**: 测试信息治理报告内容
- **加载不存在的新闻**: 测试错误处理机制

## 模拟数据

`mock-api.js` 文件包含了两个示例新闻数据：

1. **新闻 ID "1"**: 2024 企业数据安全管理指南
2. **新闻 ID "2"**: 2025 年信息治理趋势报告

可以根据需要添加更多测试数据。

## 部署到生产环境

1. 将 `CONFIG.isDevelopment` 设置为 `false`
2. 确保 API 接口 `/prod-api/api/dev-api/api/web/doc/news/{id}` 可正常访问
3. 移除或不引入 `mock-api.js` 文件
4. 测试所有功能正常工作

## 注意事项

1. **SEO 优化**: 动态内容可能影响搜索引擎索引，建议在服务端渲染关键内容
2. **加载性能**: 考虑添加加载状态指示器提升用户体验
3. **缓存策略**: 可以添加数据缓存机制减少 API 调用
4. **错误处理**: 生产环境中应提供更友好的错误提示

## 扩展功能

可以考虑添加以下功能：

- 数据缓存机制
- 加载状态指示器
- 多语言支持
- 图片懒加载
- 社交分享功能
