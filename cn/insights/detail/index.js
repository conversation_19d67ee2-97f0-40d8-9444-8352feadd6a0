// 获取新闻详情数据的函数
function getNewsDetail(newsId) {
  return fetch(`/prod-api/api/web/doc/news/${newsId}`)
    .then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`)
      }
      return res.json()
    })
    .then((data) => {
      return data.data || {}
    })
    .catch((error) => {
      console.error('获取新闻详情失败:', error)
      return {}
    })
}

// 获取新闻主题字典数据
function getDocNewTopic() {
  return fetch(`/prod-api/api/web/doc/news/type/list`)
    .then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`)
      }
      return res.json()
    })
    .then((data) => {
      const topics = data.data || []
      return topics
    })
    .catch((error) => {
      console.error('获取新闻主题失败:', error)
      throw error
    })
}

// 获取国家数据
function getCustomCountry() {
  return fetch(`/prod-api/api/web/system/dict/data/type/custom_country`)
    .then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`)
      }
      return res.json()
    })
    .then((data) => {
      const countries = data.data || []
      return countries
    })
    .catch((error) => {
      console.error('获取国家数据失败:', error)
      return []
    })
}

// 相关新闻与焦点分析
function fetchArticles(page = 1, pageSize = 4) {
  // 确保每页最多显示4个项目
  const maxPageSize = 4
  const actualPageSize = Math.min(pageSize, maxPageSize)

  return fetch(
    `/prod-api/api/web/doc/news/list?pageNum=${page}&pageSize=${actualPageSize}`,
  )
    .then((res) => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`)
      }
      return res.json()
    })
    .then((data) => {
      // 验证返回的数据结构
      if (!data || typeof data !== 'object') {
        throw new Error('无效的响应数据格式')
      }

      if (!Array.isArray(data.rows)) {
        throw new Error('文章数据格式错误')
      }

      return data
    })
    .catch((error) => {
      console.error('获取文章失败:', error)
      throw error
    })
}

// 渲染相关新闻文章
function renderRelatedArticles(articles, topics) {
  const container = document.querySelector('.crw-latest-posts .row')
  if (!container) {
    console.warn('未找到相关新闻容器')
    return
  }

  // 清空现有内容
  container.innerHTML = ''

  // 渲染每篇文章
  articles.forEach((article) => {
    const topicLabel =
      topics.find(
        (t) => article.topic && article.topic.split(',').includes(t.typeValue),
      )?.typeLabel || '未分类'

    const articleElement = document.createElement('div')
    articleElement.className = 'col-lg-3'

    const imageUrl = article.cover
      ? `https://crownwwcn.com/prod-api${article.cover}`
      : '../../wp-content/uploads/sites/7/2025/02/default-article-image.jpg'

    articleElement.innerHTML = `
      <div class="crw-article-item">
        <a href="../detail/index.html?id=${article.newsId}">
          <span class="article-top">
            <span
              data-bg="${imageUrl}"
              class="article-img rocket-lazyload"
              style="background-image: url('${imageUrl}')"
            ></span>
            <span class="article-cat">${topicLabel}</span>
            <span class="article-title">${article.title || '无标题'}</span>
          </span>
          <span class="article-bottom">
            <span class="btn-text">
              阅读更多 <i class="icon icon-arrow-button"></i>
            </span>
          </span>
        </a>
      </div>
    `

    container.appendChild(articleElement)
  })
}

// 初始化相关新闻
function initializeRelatedArticles() {
  Promise.all([
    fetchArticles(1, 4), // 获取4篇相关文章
    getDocNewTopic(), // 获取主题数据
  ])
    .then(([articlesData, topics]) => {
      if (articlesData.rows && articlesData.rows.length > 0) {
        renderRelatedArticles(articlesData.rows, topics)
        console.log('相关新闻已加载:', articlesData.rows.length, '篇文章')
      } else {
        console.warn('没有找到相关新闻数据')
      }
    })
    .catch((error) => {
      console.error('加载相关新闻失败:', error)
    })
}

// 渲染国家下拉框
function renderCountryOptions(countries) {
  const countrySelect = document.getElementById('crwCountryCode')
  if (!countrySelect) {
    console.warn('未找到国家下拉框元素')
    return
  }

  // 清空现有选项（保留默认选项）
  countrySelect.innerHTML = ''

  // 添加国家选项
  countries.forEach((country) => {
    const option = document.createElement('option')
    option.value = country.dictValue
    option.textContent = country.dictLabel

    // 如果是中国，设为默认选中
    if (country.dictLabel === '中国') {
      option.selected = true
    }

    countrySelect.appendChild(option)
  })

  console.log('国家下拉框已更新，共', countries.length, '个国家')
}

// 初始化国家下拉框
function initializeCountrySelect() {
  getCustomCountry()
    .then((countries) => {
      if (countries && countries.length > 0) {
        renderCountryOptions(countries)
      } else {
        console.warn('未获取到国家数据')
      }
    })
    .catch((error) => {
      console.error('初始化国家下拉框失败:', error)
    })
}

// 更新页面标题和元数据
function updatePageTitle(title) {
  // 更新页面标题
  document.title = `${title} - Crown Information Management China`

  // 更新meta标签
  const ogTitle = document.querySelector('meta[property="og:title"]')
  if (ogTitle) {
    ogTitle.setAttribute(
      'content',
      `${title} - Crown Information Management China`,
    )
  }

  // 更新JSON-LD结构化数据
  const jsonLdScript = document.querySelector(
    'script[type="application/ld+json"]',
  )
  if (jsonLdScript) {
    try {
      const jsonData = JSON.parse(jsonLdScript.textContent)
      if (jsonData['@graph'] && jsonData['@graph'][0]) {
        jsonData[
          '@graph'
        ][0].name = `${title} - Crown Information Management China`
      }
      if (
        jsonData['@graph'] &&
        jsonData['@graph'][2] &&
        jsonData['@graph'][2].itemListElement
      ) {
        const breadcrumbItems = jsonData['@graph'][2].itemListElement
        if (breadcrumbItems.length > 2) {
          breadcrumbItems[2].name = title
        }
      }
      jsonLdScript.textContent = JSON.stringify(jsonData)
    } catch (error) {
      console.error('更新JSON-LD数据失败:', error)
    }
  }
}

// 更新页面内容
function updatePageContent(newsData, docNewTopic) {
  // 更新面包屑导航
  const breadcrumbLast = document.querySelector('.breadcrumb_last')
  if (breadcrumbLast && newsData.title) {
    breadcrumbLast.textContent = newsData.title
  }

  // 更新主标题
  const mainTitle = document.querySelector('.crw-title h1')
  if (mainTitle && newsData.title) {
    mainTitle.textContent = newsData.title
  }

  // 更新Published状态为publishTime
  const publishStatus = document.querySelector('.p-status')
  if (publishStatus && newsData.publishTime) {
    publishStatus.textContent = newsData.publishTime
  }

  // 更新发布日期
  const publishDate = document.querySelector('.p-date')
  if (publishDate && newsData.publishDate) {
    const date = new Date(newsData.publishDate)
    const formattedDate = `${date.getFullYear()}年 ${String(
      date.getMonth() + 1,
    ).padStart(2, '0')}月 ${String(date.getDate()).padStart(2, '0')}日`
    publishDate.textContent = formattedDate
  }

  // 更新#article-content下的内容
  const articleContent = document.querySelector('#article-content')
  if (articleContent && newsData.content) {
    // 如果有HTML内容，直接使用；否则将纯文本转换为段落
    if (newsData.content.includes('<')) {
      articleContent.innerHTML = newsData.content
    } else {
      // 将纯文本按换行符分割成段落
      const paragraphs = newsData.content.split('\n').filter((p) => p.trim())
      articleContent.innerHTML = paragraphs
        .map((p) => `<p>${p.trim()}</p>`)
        .join('')
    }
  }

  // 更新特色图片
  if (newsData.cover) {
    const featuredImg = document.querySelector('.featured-img-wrap img')
    if (featuredImg) {
      featuredImg.src = `https://crownwwcn.com/prod-api${newsData.cover}`
      featuredImg.alt = newsData.title || ''
    }
  }

  // 更新分类标签
  if (newsData.topic) {
    const categoryList = document.querySelector('.post-tag-wrap ul')
    if (categoryList) {
      categoryList.innerHTML = newsData.topic
        .split(',')
        .map(
          (category) =>
            `<li><a href="../index.html?_topics=${encodeURIComponent(
              category,
            )}">${
              docNewTopic.find((item) => item.typeValue === category)
                ?.typeLabel || category
            }</a></li>`,
        )
        .join('')
    }
  }

  // 是否显示下载表单
  const downloadForm = document.querySelector('#form-container')
  if (downloadForm) {
    if (newsData.allowDownloadFile === 'Y') {
      const formIdDom = document.querySelector('#crw_form_pid')
      formIdDom.value = newsData.newsId
      const articleContainer = document.querySelector('#article-container')
      if (articleContainer) {
        articleContainer.className = 'col-lg-6'
      }
      downloadForm.style.display = 'block'
    } else {
      downloadForm.style.display = 'none'
    }
  }
}

// 从URL获取新闻ID的函数
function getNewsIdFromUrl() {
  // 可以从URL参数、路径或其他方式获取新闻ID
  const urlParams = new URLSearchParams(window.location.search)
  const newsId = urlParams.get('id')

  // 如果URL中没有ID参数，可以使用默认值或从其他地方获取
  return newsId || '1' // 默认使用ID为1的新闻
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
  const newsId = getNewsIdFromUrl()

  Promise.all([
    getDocNewTopic(), // 获取新闻主题字典数据
    getNewsDetail(newsId), // 获取新闻详情数据
  ])
    .then(([docNewTopic, newsData]) => {
      // 处理获取到的主题字典和新闻详情数据
      if (newsData && Object.keys(newsData).length > 0) {
        // 更新页面标题
        if (newsData.title) {
          updatePageTitle(newsData.title)
        }

        // 更新页面内容
        updatePageContent(newsData, docNewTopic)

        // 更新分享链接
        updateShareLinks(newsId, newsData)

        console.log('页面内容已更新:', newsData)
      } else {
        console.warn('未获取到新闻数据，使用默认内容')
      }
    })
    .catch((error) => {
      console.error('加载新闻主题失败:', error)
    })

  // 初始化表单处理器
  initializeFormHandler()

  // 初始化国家下拉框
  initializeCountrySelect()

  // 初始化相关新闻
  initializeRelatedArticles()
})

// 更新分享链接
function updateShareLinks(newsId, newsData) {
  const baseUrl = 'https://www.crownrms.com/cn/insights/detail/index.html'
  const shareUrl = `${baseUrl}?id=${newsId}`
  const title = newsData.title || '文章标题'
  const encodedTitle = encodeURIComponent(title)
  const encodedUrl = encodeURIComponent(shareUrl)

  // 更新LinkedIn分享链接
  const linkedinShare = document.querySelector('#linkedin-share')
  if (linkedinShare) {
    linkedinShare.href = `https://www.linkedin.com/shareArticle?mini=true&url=${encodedUrl}&title=${encodedTitle}`
  }

  // 更新Twitter/X分享链接
  const twitterShare = document.querySelector('#twitter-share')
  if (twitterShare) {
    twitterShare.href = `https://x.com/intent/post?url=${encodedUrl}&text=${encodedTitle}`
  }

  // 更新Facebook分享链接
  const facebookShare = document.querySelector('#facebook-share')
  if (facebookShare) {
    facebookShare.href = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`
  }

  // 更新复制链接
  const copyLink = document.querySelector('#copy-link')
  if (copyLink) {
    copyLink.href = shareUrl

    // 添加复制到剪贴板功能
    copyLink.addEventListener('click', function (e) {
      e.preventDefault()

      // 尝试使用现代的Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard
          .writeText(shareUrl)
          .then(() => {
            showCopyMessage('链接已复制到剪贴板！')
          })
          .catch(() => {
            fallbackCopyTextToClipboard(shareUrl)
          })
      } else {
        // 降级方案
        fallbackCopyTextToClipboard(shareUrl)
      }
    })
  }
}

// 显示复制成功消息
function showCopyMessage(message) {
  // 创建临时提示元素
  const toast = document.createElement('div')
  toast.textContent = message
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  `

  document.body.appendChild(toast)

  // 3秒后移除提示
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast)
    }
  }, 3000)
}

// 降级复制方案
function fallbackCopyTextToClipboard(text) {
  const textArea = document.createElement('textarea')
  textArea.value = text
  textArea.style.position = 'fixed'
  textArea.style.left = '-999999px'
  textArea.style.top = '-999999px'
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()

  try {
    const successful = document.execCommand('copy')
    if (successful) {
      showCopyMessage('链接已复制到剪贴板！')
    } else {
      showCopyMessage('复制失败，请手动复制链接')
    }
  } catch (err) {
    showCopyMessage('复制失败，请手动复制链接')
  }

  document.body.removeChild(textArea)
}

// 表单处理相关函数
function initializeFormHandler() {
  const form = document.getElementById('customerInfoForm')
  const submitBtn = document.getElementById('submitBtn')

  if (form && submitBtn) {
    submitBtn.addEventListener('click', function (e) {
      e.preventDefault()

      // 验证表单
      if (!validateForm()) {
        return
      }

      // 收集表单数据
      const formData = new FormData(form)
      const data = {}

      // 将FormData转换为普通对象
      for (let [key, value] of formData.entries()) {
        data[key] = value
      }

      // 添加隐藏字段的值
      data.crw_form_pid = document.getElementById('crw_form_pid').value
      data.crw_crpurl = document.querySelector('input[name="crw_crpurl"]').value
      data.crw_phti = document.getElementById('crw_phti').value
      data.crw_gsrc = document.getElementById('crw_gsrc').value

      // 禁用提交按钮，防止重复提交
      submitBtn.disabled = true
      submitBtn.textContent = '提交中...'

      // 发送请求
      fetch('/prod-api/api/web/customer/info', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error('网络请求失败')
          }
          return response.json()
        })
        .then(() => {
          // 处理成功响应
          showSuccessMessage('提交成功！')
          form.reset()
          const downloadDom = document.getElementById('download-container')
          downloadDom.style.display = 'block'

          const newsId = getNewsIdFromUrl()

          downloadDom
            .querySelector('.download-btn')
            .addEventListener('click', () => {
              // 处理PDF文件下载逻辑
              downloadPdfFile(newsId)
            })

          document.getElementById('form-content').style.display = 'none'
        })
        .catch((error) => {
          // 处理错误
          console.error('提交失败:', error)
          showErrorMessage('提交失败，请稍后重试')
        })
        .finally(() => {
          // 恢复提交按钮
          submitBtn.disabled = false
          submitBtn.textContent = '提交'
        })
    })
  }
}

// 表单验证函数
function validateForm() {
  const requiredFields = [
    'crwFirstName',
    'crwLastName',
    'crwJobTitle',
    'crwCompany',
    'crwCountryCode',
    'crwEmail',
  ]

  let isValid = true

  // 清除之前的错误样式
  document.querySelectorAll('.error').forEach((el) => {
    el.style.display = 'none'
  })

  // 验证必填字段
  requiredFields.forEach((fieldName) => {
    const field = document.getElementById(fieldName)
    if (field && !field.value.trim()) {
      isValid = false
      field.style.borderColor = 'red'
      // 可以在这里添加错误提示
    } else if (field) {
      field.style.borderColor = ''
    }
  })

  // 验证邮箱格式
  const emailField = document.getElementById('crwEmail')
  if (emailField && emailField.value.trim()) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(emailField.value.trim())) {
      isValid = false
      emailField.style.borderColor = 'red'
      showErrorMessage('请输入有效的邮箱地址')
    }
  }

  if (!isValid) {
    showErrorMessage('请填写所有必填字段')
  }

  return isValid
}

// 显示成功消息
function showSuccessMessage(message) {
  showMessage(message, '#28a745')
}

// 显示错误消息
function showErrorMessage(message) {
  showMessage(message, '#dc3545')
}

// 通用消息显示函数
function showMessage(message, backgroundColor) {
  // 创建临时提示元素
  const toast = document.createElement('div')
  toast.textContent = message
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${backgroundColor};
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  `

  document.body.appendChild(toast)

  // 3秒后移除提示
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast)
    }
  }, 3000)
}

// PDF文件下载函数
function downloadPdfFile(newsId) {
  // 显示下载中提示
  showMessage('正在准备下载...', '#007bff')

  // 禁用下载按钮，防止重复点击
  const downloadBtn = document.querySelector('.download-btn')
  if (downloadBtn) {
    downloadBtn.disabled = true
    downloadBtn.textContent = '下载中...'
  }

  fetch(`/prod-api/api/web/doc/news/${newsId}/file/download`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      // 检查响应头中的内容类型
      const contentType = response.headers.get('content-type')
      if (!contentType || !contentType.includes('application/pdf')) {
        console.warn('响应可能不是PDF文件，内容类型:', contentType)
      }

      // 获取文件名（从响应头或使用默认名称）
      const contentDisposition = response.headers.get('content-disposition')
      let filename = 'document.pdf'

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(
          /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/,
        )
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '')
        }
      }

      // 将响应转换为Blob
      return response.blob().then((blob) => ({ blob, filename }))
    })
    .then(({ blob, filename }) => {
      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = filename

      // 添加到DOM并触发下载
      document.body.appendChild(a)
      a.click()

      // 清理
      setTimeout(() => {
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)
      }, 100)

      showSuccessMessage('文件下载已开始')
    })
    .catch((error) => {
      console.error('下载失败:', error)
      showErrorMessage('下载失败，请稍后重试')
    })
    .finally(() => {
      // 恢复下载按钮
      if (downloadBtn) {
        downloadBtn.disabled = false
        downloadBtn.textContent = '下载文件'
      }
    })
}
