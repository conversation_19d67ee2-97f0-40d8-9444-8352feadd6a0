// Google Tag Manager (仅在生产环境中加载)
if (
  window.location.hostname !== 'localhost' &&
  window.location.hostname !== '127.0.0.1'
) {
  ;(function (w, d, s, l, i) {
    w[l] = w[l] || []
    w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' })
    var f = d.getElementsByTagName(s)[0],
      j = d.createElement(s),
      dl = l != 'dataLayer' ? '&l=' + l : ''
    j.async = true
    j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl
    f.parentNode.insertBefore(j, f)
  })(window, document, 'script', 'dataLayer', 'GTM-W4DR9R')
}

// OneTrust Cookies Consent Notice
function OptanonWrapper() {}

// 全局变量：当前选择的主题
let currentSelectedTopic = null

// 全局变量：当前搜索词
let currentSearchTerm = null

// 动态渲染文章列表
function renderArticleItems(articles) {
  const container = document.querySelector('.facetwp-template .row')
  if (!container) {
    console.error('找不到文章容器')
    return
  }

  // 清空现有内容（保留 fwp-loop 注释）
  container.innerHTML = '<!--fwp-loop-->'

  // 遍历文章数据并生成HTML
  articles.forEach((article, index) => {
    const articleElement = createArticleElement(article)
    container.appendChild(articleElement)

    // 添加渐入动画效果
    setTimeout(() => {
      const articleItem = articleElement.querySelector('.crw-article-item')
      if (articleItem) {
        articleItem.classList.add('loaded')
      }
    }, index * 100) // 每个元素延迟100ms显示
  })
}

// 创建单个文章元素
function createArticleElement(article) {
  const colDiv = document.createElement('div')
  colDiv.className = 'col-lg-3'

  colDiv.innerHTML = `
            <div class="crw-article-item">
                <a href="./detail/index.html?id=${article.newsId}">
                    <span class="article-top">
                        <span class="article-img" style="background-image: url('https://crownwwcn.com/prod-api${
                          article.cover ||
                          '../wp-content/uploads/sites/7/2025/03/default-article.jpg'
                        }');"></span>
                        <span class="article-cat">${
                          article.topic || '未分类'
                        }</span>
                        <span class="article-title">${
                          article.title || '无标题'
                        }</span>
                    </span>
                    <span class="article-bottom">
                        <span class="btn-text">阅读更多 <i class="icon icon-arrow-button"></i></span>
                    </span>
                </a>
            </div>
        `

  return colDiv
}

// 显示加载状态
function showLoading() {
  const container = document.querySelector('.facetwp-template .row')
  if (container) {
    container.innerHTML = `
                <!--fwp-loop-->
                <div class="col-lg-12 text-center">
                    <div class="loading-spinner">
                        <p>正在加载文章...</p>
                    </div>
                </div>
            `
  }
}

// 显示错误状态
function showError(message) {
  const container = document.querySelector('.facetwp-template .row')
  if (container) {
    container.innerHTML = `
                <!--fwp-loop-->
                <div class="container facetwp-template">
            <!--fwp-loop-->
                <br>
                <h3 class="text-center">${message}</h3>
                <br>
                    </div>
            `
  }
}

// 生成分页HTML
function generatePaginationHTML(currentPage, totalPages) {
  if (totalPages <= 1) {
    return '<div class="facetwp-pager"></div>'
  }

  let paginationHTML = '<div class="facetwp-pager">'

  // 上一页按钮
  if (currentPage > 1) {
    paginationHTML += `<a class="facetwp-page prev" data-page="${
      currentPage - 1
    }">« 上一页</a>`
  }

  // 页码逻辑：显示当前页前后的页码
  let startPage = Math.max(1, currentPage - 2)
  let endPage = Math.min(totalPages, currentPage + 2)

  // 确保至少显示5个页码（如果总页数允许）
  if (endPage - startPage < 4) {
    if (startPage === 1) {
      endPage = Math.min(totalPages, startPage + 4)
    } else if (endPage === totalPages) {
      startPage = Math.max(1, endPage - 4)
    }
  }

  // 如果起始页不是1，显示第一页和省略号
  if (startPage > 1) {
    paginationHTML += `<a class="facetwp-page first" data-page="1">1</a>`
    if (startPage > 2) {
      paginationHTML += `<span class="facetwp-page dots">...</span>`
    }
  }

  // 页码
  for (let i = startPage; i <= endPage; i++) {
    const isActive = i === currentPage
    let pageClass = 'facetwp-page'
    if (isActive) {
      pageClass += ' active'
    }
    if (i === 1 && startPage === 1) {
      pageClass += ' first'
    }
    if (i === totalPages && endPage === totalPages) {
      pageClass += ' last'
    }
    paginationHTML += `<a class="${pageClass}" data-page="${i}">${i}</a>`
  }

  // 如果结束页不是最后一页，显示省略号和最后一页
  if (endPage < totalPages) {
    if (endPage < totalPages - 1) {
      paginationHTML += `<span class="facetwp-page dots">...</span>`
    }
    paginationHTML += `<a class="facetwp-page last" data-page="${totalPages}">${totalPages}</a>`
  }

  // 下一页按钮
  if (currentPage < totalPages) {
    paginationHTML += `<a class="facetwp-page next" data-page="${
      currentPage + 1
    }">下一页 »</a>`
  }

  paginationHTML += '</div>'
  return paginationHTML
}

// 渲染分页到页面
function renderPagination(paginationHTML) {
  const paginationContainer = document.querySelector(
    '.facetwp-facet-pagination',
  )
  if (paginationContainer) {
    paginationContainer.innerHTML = paginationHTML

    // 添加分页点击事件监听
    setupPaginationEvents()
  }
}

// 设置分页点击事件
function setupPaginationEvents() {
  // 移除所有现有的分页事件监听器
  removePaginationEvents()

  const paginationContainer = document.querySelector(
    '.facetwp-facet-pagination',
  )
  if (!paginationContainer) return

  // 使用事件委托处理所有分页点击
  paginationContainer.addEventListener('click', handlePaginationClick, true)

  // 阻止分页容器内的其他事件冒泡
  paginationContainer.addEventListener('mousedown', preventOtherEvents, true)
  paginationContainer.addEventListener('mouseup', preventOtherEvents, true)
  paginationContainer.addEventListener('dblclick', preventOtherEvents, true)
  paginationContainer.addEventListener('contextmenu', preventOtherEvents, true)
}

// 处理分页点击事件
function handlePaginationClick(e) {
  // 阻止默认行为和事件冒泡
  e.preventDefault()
  e.stopPropagation()
  e.stopImmediatePropagation()

  const target = e.target.closest('.facetwp-page')
  if (!target) return

  // 检查是否是有效的分页链接
  if (!target.hasAttribute('data-page')) return

  const page = parseInt(target.getAttribute('data-page'))

  // 验证页码有效性
  if (!page || page < 1) return

  // 检查是否是当前激活页或省略号
  if (
    target.classList.contains('active') ||
    target.classList.contains('dots')
  ) {
    return
  }

  // 检查是否正在加载中
  // if (target.closest('.facetwp-facet').classList.contains('is-loading')) {
  //   return
  // }

  // 添加点击效果
  target.classList.add('clicking')
  setTimeout(() => {
    target.classList.remove('clicking')
  }, 150)

  // 滚动到页面顶部
  scrollToTop()

  // 根据当前状态决定加载方式
  if (currentSearchTerm) {
    // 如果有搜索词，使用搜索分页加载
    console.log(`分页点击：搜索"${currentSearchTerm}"的第${page}页`)
    showLoading()
    performSearch(currentSearchTerm, page)
      .then((result) => {
        if (result && result.articles && result.articles.length > 0) {
          renderArticleItems(result.articles)
          updatePaginationForSearch(result.pagination, currentSearchTerm)
        } else {
          showError('该页面暂无文章')
        }
      })
      .catch((error) => {
        console.error('搜索分页失败:', error)
        showError('搜索分页失败: ' + error.message)
      })
  } else if (currentSelectedTopic) {
    // 如果有选择主题，使用主题筛选加载
    console.log(`分页点击：加载主题"${currentSelectedTopic}"的第${page}页`)
    showLoading()
    filterArticlesByTopic(currentSelectedTopic, page)
      .then((result) => {
        if (result && result.articles && result.articles.length > 0) {
          renderArticleItems(result.articles)
          updatePaginationForTopic(result.pagination)
        } else {
          showError('该页面暂无文章')
        }
      })
      .catch((error) => {
        console.error('加载分页失败:', error)
        showError('加载分页失败: ' + error.message)
      })
  } else {
    // 如果没有搜索词和主题选择，使用默认的分页加载
    if (window.loadArticlesByPage) {
      window.loadArticlesByPage(page)
    } else {
      console.error('loadArticlesByPage 函数未定义')
    }
  }
}

// 阻止其他事件
function preventOtherEvents(e) {
  const target = e.target.closest('.facetwp-page')
  if (target) {
    e.preventDefault()
    e.stopPropagation()
    e.stopImmediatePropagation()
  }
}

// 移除分页事件监听器
function removePaginationEvents() {
  const paginationContainer = document.querySelector(
    '.facetwp-facet-pagination',
  )
  if (paginationContainer) {
    // 克隆节点以移除所有事件监听器
    const newContainer = paginationContainer.cloneNode(true)
    paginationContainer.parentNode.replaceChild(
      newContainer,
      paginationContainer,
    )
  }
}

// 滚动到页面顶部
function scrollToTop() {
  const articlesSection = document.querySelector('.facetwp-template')
  if (articlesSection) {
    articlesSection.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  } else {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  }
}

// 配置FacetWP和懒加载
function setupFacetWP(topics, { page, total, totalPage }) {
  // 动态加载 FacetWP 前端脚本
  const script = document.createElement('script')
  script.src =
    '../wp-content/plugins/facetwp/assets/js/dist/front.min7035.js?ver=4.3.4'
  script.async = true
  script.onload = function () {
    console.log('FacetWP 脚本加载成功')
  }
  script.onerror = function () {
    console.error('FacetWP 脚本加载失败')
  }
  document.head.appendChild(script)

  // 生成动态分页HTML
  const paginationHTML = generatePaginationHTML(page, totalPage)

  window.FWP_JSON = {
    prefix: '_',
    no_results_text: 'No results found',
    ajaxurl: 'https://www.crownrms.com/cn/wp-json/facetwp/v1/refresh',
    nonce: '6af19a93be',
    preload_data: {
      facets: {
        search:
          '<span class="facetwp-input-wrap"><i class="facetwp-icon" data-ts="\u641c\u7d22"></i><input type="text" class="facetwp-search" value="" placeholder="\u8f93\u5165\u5173\u952e\u5b57" autocomplete="off" /></span>',
        topics: `
        <select class="facetwp-dropdown"><option value="">\u6240\u6709\u4e3b\u9898<\/option>
        ${topics
          .map((topic) => {
            // 检查是否应该默认选中这个选项
            const isSelected =
              window.urlTopicParam === topic.typeValue ? ' selected' : ''
            return `<option value="${topic.typeValue}" data-counter="${topic.newsCount}" class="d0"${isSelected}>${topic.typeLabel}</option>`
          })
          .join('')}
        </select>
        `,
        industries:
          '<select class="facetwp-dropdown"><option value="">\u6240\u6709\u884c\u4e1a</option></select>',
        pagination: paginationHTML,
      },
      template: '',
      settings: {
        debug: 'Enable debug mode in [Settings > FacetWP > Settings]',
        pager: {
          page: page,
          per_page: 12,
          total_rows: total,
          total_rows_unfiltered: total,
          total_pages: totalPage,
        },
        num_choices: { topics: 10, industries: 0 },
        labels: {
          search: 'Search',
          topics: 'Topics',
          industries: 'Industries',
          pagination: 'Pagination',
        },
        search: { auto_refresh: 'no' },
        topics: {
          placeholder: '\u6240\u6709\u4e3b\u9898',
          overflowText: '{n} selected',
          searchText: 'Search',
          noResultsText: 'No results found',
          operator: 'and',
        },
        industries: {
          placeholder: '\u6240\u6709\u884c\u4e1a',
          overflowText: '{n} selected',
          searchText: 'Search',
          noResultsText: 'No results found',
          operator: 'and',
        },
        pagination: {
          pager_type: 'numbers',
          scroll_target: '',
          scroll_offset: 0,
        },
      },
    },
  }
  window.FWP_HTTP = { get: [], uri: 'cn/insights', url_vars: [] }

  // 渲染分页到页面
  renderPagination(paginationHTML)

  // 设置主题下拉框事件监听器
  setupTopicsDropdownEvents()

  // 设置搜索框事件监听器
  setupSearchEvents()

  // 如果URL中有_topics参数，设置当前选择的主题
  if (window.urlTopicParam) {
    currentSelectedTopic = window.urlTopicParam
    console.log('根据URL参数设置当前选择的主题:', currentSelectedTopic)
  }

  setupLazyLoad()
}

// 设置主题下拉框事件监听器
function setupTopicsDropdownEvents() {
  // 使用事件委托监听下拉框变化
  document.addEventListener('change', function (e) {
    // 检查是否是topics下拉框
    if (e.target.matches('.facetwp-facet-topics .facetwp-dropdown')) {
      const selectedValue = e.target.value
      console.log('主题下拉框选择变化:', selectedValue)

      // 更新当前选择的主题
      currentSelectedTopic = selectedValue || null

      // 如果选择了特定主题，根据主题筛选文章
      if (selectedValue) {
        handleTopicSelection(selectedValue)
      } else {
        // 如果选择"所有主题"，重新加载所有文章
        handleAllTopicsSelection()
      }
    }
  })
}

// 设置搜索框事件监听器
function setupSearchEvents() {
  // 监听搜索框输入事件（支持回车键搜索）
  document.addEventListener('keyup', function (e) {
    if (e.target.matches('.facetwp-facet-search .facetwp-search')) {
      // 如果按下回车键，执行搜索
      if (e.key === 'Enter') {
        const searchTerm = e.target.value.trim()
        handleSearchInput(searchTerm)
      }
    }
  })

  // 监听搜索图标点击事件
  document.addEventListener('click', function (e) {
    if (e.target.matches('.facetwp-facet-search .facetwp-icon')) {
      const searchInput = e.target
        .closest('.facetwp-facet-search')
        .querySelector('.facetwp-search')
      if (searchInput) {
        const searchTerm = searchInput.value.trim()
        handleSearchInput(searchTerm)
      }
    }
  })
}

// 处理搜索输入
function handleSearchInput(searchTerm) {
  console.log('处理搜索输入:', searchTerm)

  // 更新当前搜索词
  currentSearchTerm = searchTerm || null

  // 如果有搜索词，执行搜索
  if (searchTerm) {
    // 显示加载状态
    showLoading()

    // 重置主题选择（搜索时不考虑主题筛选）
    currentSelectedTopic = null
    resetTopicsDropdown()

    // 执行搜索
    performSearch(searchTerm)
      .then((result) => {
        if (result && result.articles && result.articles.length > 0) {
          // 渲染搜索结果
          renderArticleItems(result.articles)

          // 更新分页信息
          updatePaginationForSearch(result.pagination, searchTerm)
        } else {
          showError(`没有找到包含"${searchTerm}"的文章`)
          // 清空分页
          clearPagination()
        }
      })
      .catch((error) => {
        console.error('搜索失败:', error)
        showError('搜索失败: ' + error.message)
        // 清空分页
        clearPagination()
      })
  } else {
    // 如果搜索词为空，重新加载所有文章
    handleClearSearch()
  }
}

// 处理主题选择
function handleTopicSelection(topicValue) {
  console.log('处理主题选择:', topicValue)

  // 清空搜索词
  currentSearchTerm = null
  clearSearchInput()

  // 显示加载状态
  showLoading()

  // 根据选择的主题筛选文章
  filterArticlesByTopic(topicValue)
    .then((result) => {
      if (result && result.articles && result.articles.length > 0) {
        // 渲染文章
        renderArticleItems(result.articles)

        // 更新分页信息
        updatePaginationForTopic(result.pagination)
      } else {
        showError('该主题下暂无文章')
        // 清空分页
        clearPagination()
      }
    })
    .catch((error) => {
      console.error('筛选文章失败:', error)
      showError('筛选文章失败: ' + error.message)
      // 清空分页
      clearPagination()
    })
}

// 执行搜索
async function performSearch(searchTerm, pageNum = 1) {
  try {
    // 动态导入搜索函数
    const { searchArticlesByTitle } = await import('./api.js')

    // 调用搜索接口
    const result = await searchArticlesByTitle(searchTerm, pageNum)
    return result
  } catch (error) {
    console.error('执行搜索失败:', error)
    throw error
  }
}

// 更新搜索结果的分页信息
function updatePaginationForSearch(paginationInfo, searchTerm) {
  const { page, total, totalPage } = paginationInfo

  // 生成新的分页HTML
  const paginationHTML = generatePaginationHTML(page, totalPage)

  // 更新FWP_JSON中的分页设置
  if (window.FWP_JSON && window.FWP_JSON.preload_data) {
    window.FWP_JSON.preload_data.settings.pager = {
      page: page,
      per_page: 12,
      total_rows: total,
      total_rows_unfiltered: total,
      total_pages: totalPage,
    }

    // 更新分页HTML
    window.FWP_JSON.preload_data.facets.pagination = paginationHTML
  }

  // 渲染分页到页面
  renderPagination(paginationHTML)

  console.log(
    `搜索分页已更新: 第${page}页，共${totalPage}页，总计${total}条记录，搜索词: "${searchTerm}"`,
  )
}

// 重置主题下拉框
function resetTopicsDropdown() {
  const topicsDropdown = document.querySelector(
    '.facetwp-facet-topics .facetwp-dropdown',
  )
  if (topicsDropdown) {
    topicsDropdown.value = ''
  }
}

// 清空搜索输入框
function clearSearchInput() {
  const searchInput = document.querySelector(
    '.facetwp-facet-search .facetwp-search',
  )
  if (searchInput) {
    searchInput.value = ''
  }
}

// 处理清空搜索
function handleClearSearch() {
  console.log('清空搜索，重新加载所有文章')

  // 重置搜索词
  currentSearchTerm = null

  // 重新加载所有文章
  if (window.loadArticlesByPage) {
    window.loadArticlesByPage(1)
  }
}

// 处理"所有主题"选择
function handleAllTopicsSelection() {
  console.log('选择所有主题，重新加载文章')

  // 重置当前选择的主题
  currentSelectedTopic = null

  // 清空搜索词
  currentSearchTerm = null
  clearSearchInput()

  // 重新加载所有文章
  if (window.loadArticlesByPage) {
    window.loadArticlesByPage(1)
  }
}

// 根据主题筛选文章
async function filterArticlesByTopic(topicValue, pageNum = 1) {
  try {
    // 获取文章列表，这里可以添加主题筛选参数
    const response = await fetch(
      `/prod-api/api/web/doc/news/list?pageNum=${pageNum}&pageSize=12&topic=${encodeURIComponent(
        topicValue,
      )}`,
    )

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data || !Array.isArray(data.rows)) {
      throw new Error('文章数据格式错误')
    }

    // 获取缓存的主题数据来映射主题名称
    const topics = window.cachedTopics || []

    // 处理文章数据，添加主题标签
    const articles = data.rows.map((item) => ({
      ...item,
      topic:
        topics.find(
          (t) => item.topic && item.topic.split(',').includes(t.typeValue),
        )?.typeLabel || '未分类',
    }))

    // 计算分页信息
    const total = data.total || 0
    const totalPage = Math.ceil(total / 12)
    const currentPage = pageNum

    return {
      articles,
      pagination: {
        page: currentPage,
        total,
        totalPage,
      },
    }
  } catch (error) {
    console.error('筛选文章失败:', error)
    throw error
  }
}

// 更新主题筛选的分页信息
function updatePaginationForTopic(paginationInfo) {
  const { page, total, totalPage } = paginationInfo

  // 生成新的分页HTML
  const paginationHTML = generatePaginationHTML(page, totalPage)

  // 更新FWP_JSON中的分页设置
  if (window.FWP_JSON && window.FWP_JSON.preload_data) {
    window.FWP_JSON.preload_data.settings.pager = {
      page: page,
      per_page: 12,
      total_rows: total,
      total_rows_unfiltered: total,
      total_pages: totalPage,
    }

    // 更新分页HTML
    window.FWP_JSON.preload_data.facets.pagination = paginationHTML
  }

  // 渲染分页到页面
  renderPagination(paginationHTML)

  console.log(`分页已更新: 第${page}页，共${totalPage}页，总计${total}条记录`)
}

// 清空分页
function clearPagination() {
  const emptyPaginationHTML = '<div class="facetwp-pager"></div>'

  // 更新FWP_JSON中的分页设置
  if (window.FWP_JSON && window.FWP_JSON.preload_data) {
    window.FWP_JSON.preload_data.settings.pager = {
      page: 1,
      per_page: 12,
      total_rows: 0,
      total_rows_unfiltered: 0,
      total_pages: 0,
    }

    // 清空分页HTML
    window.FWP_JSON.preload_data.facets.pagination = emptyPaginationHTML
  }

  // 渲染空分页到页面
  renderPagination(emptyPaginationHTML)

  console.log('分页已清空')
}

// 设置懒加载
function setupLazyLoad() {
  window.lazyLoadOptions = [
    {
      elements_selector:
        'img[data-lazy-src],.rocket-lazyload,iframe[data-lazy-src]',
      data_src: 'lazy-src',
      data_srcset: 'lazy-srcset',
      data_sizes: 'lazy-sizes',
      class_loading: 'lazyloading',
      class_loaded: 'lazyloaded',
      threshold: 300,
      callback_loaded: function (element) {
        if (
          element.tagName === 'IFRAME' &&
          element.dataset.rocketLazyload == 'fitvidscompatible'
        ) {
          if (element.classList.contains('lazyloaded')) {
            if (typeof window.jQuery != 'undefined') {
              if (jQuery.fn.fitVids) {
                jQuery(element).parent().fitVids()
              }
            }
          }
        }
      },
    },
    {
      elements_selector: '.rocket-lazyload',
      data_src: 'lazy-src',
      data_srcset: 'lazy-srcset',
      data_sizes: 'lazy-sizes',
      class_loading: 'lazyloading',
      class_loaded: 'lazyloaded',
      threshold: 300,
    },
  ]

  window.addEventListener(
    'LazyLoad::Initialized',
    function (e) {
      var lazyLoadInstance = e.detail.instance
      if (window.MutationObserver) {
        var observer = new MutationObserver(function (mutations) {
          var image_count = 0
          var iframe_count = 0
          var rocketlazy_count = 0
          mutations.forEach(function (mutation) {
            for (var i = 0; i < mutation.addedNodes.length; i++) {
              if (
                typeof mutation.addedNodes[i].getElementsByTagName !==
                'function'
              ) {
                continue
              }
              if (
                typeof mutation.addedNodes[i].getElementsByClassName !==
                'function'
              ) {
                continue
              }
              images = mutation.addedNodes[i].getElementsByTagName('img')
              is_image = mutation.addedNodes[i].tagName == 'IMG'
              iframes = mutation.addedNodes[i].getElementsByTagName('iframe')
              is_iframe = mutation.addedNodes[i].tagName == 'IFRAME'
              rocket_lazy =
                mutation.addedNodes[i].getElementsByClassName('rocket-lazyload')
              image_count += images.length
              iframe_count += iframes.length
              rocketlazy_count += rocket_lazy.length
              if (is_image) {
                image_count += 1
              }
              if (is_iframe) {
                iframe_count += 1
              }
            }
          })
          if (image_count > 0 || iframe_count > 0 || rocketlazy_count > 0) {
            lazyLoadInstance.update()
          }
        })
        var b = document.getElementsByTagName('body')[0]
        var config = { childList: !0, subtree: !0 }
        observer.observe(b, config)
      }
    },
    !1,
  )
}

// 导出函数
export {
  renderArticleItems,
  createArticleElement,
  showLoading,
  showError,
  setupFacetWP,
  generatePaginationHTML,
  renderPagination,
  setupPaginationEvents,
  handlePaginationClick,
  preventOtherEvents,
  removePaginationEvents,
  scrollToTop,
  setupTopicsDropdownEvents,
  setupSearchEvents,
  handleSearchInput,
  handleTopicSelection,
  handleAllTopicsSelection,
  handleClearSearch,
  performSearch,
  filterArticlesByTopic,
  updatePaginationForTopic,
  updatePaginationForSearch,
  resetTopicsDropdown,
  clearSearchInput,
  clearPagination,
}
