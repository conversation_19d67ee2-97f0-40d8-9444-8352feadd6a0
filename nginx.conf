server {
    listen 80;
    server_name localhost;
    
    # 在重定向中保留端口号
    port_in_redirect on;
    
    # 设置根目录
    root /usr/share/nginx/html;
    index index.html index.htm;
    
    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 静态文件缓存设置
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|eot|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # API代理配置 - 将 /prod-api 请求代理到后端服务
    location /prod-api/ {
        proxy_pass https://crownwwcn.com/prod-api/;
        proxy_set_header Host crownwwcn.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 处理跨域
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS, PUT, DELETE';
        add_header Access-Control-Allow-Headers 'Accept,Authorization,Cache-Control,Content-Type,DNT,If-Modified-Since,Keep-Alive,Origin,User-Agent,X-Requested-With';
        
        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            return 204;
        }
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 根路径重定向到中文页面
    location = / {
        return 301 $scheme://$http_host/cn/;
    }
    
    # 处理中文路径
    location /cn/ {
        try_files $uri $uri/ /cn/index.html;
        
        # 禁用HTML文件缓存
        location ~* \.html$ {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
    }
    
    # HTML文件处理
    location / {
        try_files $uri $uri/ =404;
        
        # 禁用HTML文件缓存
        location ~* \.html$ {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
    }
    
    # WordPress相关文件处理
    location /wp-content/ {
        try_files $uri $uri/ /cn/wp-content;
    }
    
    location /wp-includes/ {
        expires 1y;
        add_header Cache-Control "public";
        try_files $uri /cn/wp-includes/$uri /wp-includes/$uri =404;
    }
    
    location /wp-json/ {
        try_files $uri /cn/wp-json/$uri /wp-json/$uri =404;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # 安全设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # 隐藏nginx版本
    server_tokens off;
} 