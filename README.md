# Crown RMS Web 服务 Docker 部署

这个项目使用Docker容器化部署Crown RMS静态网站，并配置Nginx作为前端服务器，将API请求代理到后端服务。

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）

```bash
# 给脚本执行权限
chmod +x start.sh

# 运行启动脚本
./start.sh
```

### 方法二：使用Docker Compose

```bash
# 构建并启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 方法三：使用Docker命令

```bash
# 构建镜像
docker build -t crownrms-web .

# 运行容器
docker run -d -p 8080:80 --name crownrms-web crownrms-web
```

## 🌐 访问地址

启动成功后，你可以通过以下地址访问网站：

- **主页**: http://localhost:8080 （自动重定向到中文页面）
- **中文页面**: http://localhost:8080/cn/
- **客户中心**: http://localhost:8080/cn/customer-centre/
- **联系我们**: http://localhost:8080/cn/contact-us/
- **关于我们**: http://localhost:8080/cn/about-us/

## 🔧 API代理配置

所有发送到 `/prod-api/*` 的请求将被自动代理到 `https://crownwwcn.com/prod-api/*`

例如：
- `http://localhost:8080/prod-api/user/login` → `https://crownwwcn.com/prod-api/user/login`
- `http://localhost:8080/prod-api/data/list` → `https://crownwwcn.com/prod-api/data/list`

## 📋 项目结构

```
.
├── Dockerfile              # Docker镜像构建文件
├── docker-compose.yml      # Docker Compose配置文件
├── nginx.conf              # Nginx服务器配置
├── start.sh               # 快速启动脚本
├── .dockerignore          # Docker构建忽略文件
├── README.md              # 说明文档
├── cn/                    # 中文网站文件
│   ├── index.html
│   ├── wp-content/
│   └── ...
├── favicon-16x16.png      # 网站图标
├── favicon-32x32.png
└── apple-touch-icon.png
```

## 🛠️ 配置说明

### Nginx配置特性

- **静态文件服务**: 服务所有HTML、CSS、JS、图片等静态文件
- **Gzip压缩**: 自动压缩响应内容，提高传输效率
- **缓存策略**: 
  - 静态资源（图片、CSS、JS）缓存1年
  - HTML文件禁用缓存，确保内容更新
- **API代理**: 将 `/prod-api/` 请求代理到 `https://crownwwcn.com/prod-api/`
- **跨域支持**: 配置CORS头部支持跨域请求
- **安全头部**: 添加安全相关的HTTP头部

### 端口配置

- **容器内部端口**: 80
- **外部访问端口**: 8080

如需修改外部端口，编辑 `docker-compose.yml` 文件中的端口映射：

```yml
ports:
  - "你的端口:80"
```

## 📝 常用命令

```bash
# 查看运行状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 重新构建并启动
docker-compose up -d --build

# 进入容器
docker-compose exec crownrms-web sh

# 查看Nginx配置
docker-compose exec crownrms-web cat /etc/nginx/conf.d/default.conf
```

## 🐛 故障排除

### 服务无法启动

1. 检查Docker是否正在运行
2. 检查端口8080是否被占用
3. 查看日志: `docker-compose logs`

### 无法访问网站

1. 确认容器正在运行: `docker-compose ps`
2. 检查端口映射是否正确
3. 尝试访问: http://localhost:8080

### API代理不工作

1. 检查Nginx配置: `docker-compose exec crownrms-web cat /etc/nginx/conf.d/default.conf`
2. 查看Nginx错误日志: `docker-compose logs crownrms-web`
3. 确认后端API地址是否可访问

## 📊 性能优化

- 使用了Alpine Linux基础镜像，镜像体积小
- 启用了Gzip压缩，减少传输数据量
- 配置了合理的缓存策略
- 使用了高效的Nginx服务器

## 🔒 安全特性

- 隐藏Nginx版本信息
- 添加安全HTTP头部
- 配置了合理的代理头部
- 禁用了目录浏览

## 📄 许可证

本项目仅用于Crown RMS网站的Docker化部署。 