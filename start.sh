#!/bin/bash

# Crown RMS Web服务启动脚本

echo "🚀 启动 Crown RMS Web 服务..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 停止并删除现有容器（如果存在）
echo "🔄 清理现有容器..."
docker-compose down

# 构建并启动服务
echo "🏗️  构建镜像..."
docker-compose build

echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 3

# 检查服务状态
if docker-compose ps | grep -q "Up"; then
    echo ""
    echo "✅ Crown RMS Web 服务启动成功！"
    echo ""
    echo "🌐 访问地址:"
    echo "   主页: http://localhost:8080"
    echo "   中文页面: http://localhost:8080/cn/"
    echo ""
    echo "🔧 API代理:"
    echo "   本地API请求 /prod-api/* 将被代理到 https://crownwwcn.com/prod-api/*"
    echo ""
    echo "📊 查看服务状态: docker-compose ps"
    echo "📝 查看日志: docker-compose logs -f"
    echo "🛑 停止服务: docker-compose down"
else
    echo "❌ 服务启动失败，请检查日志: docker-compose logs"
    exit 1
fi 